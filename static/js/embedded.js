document.addEventListener('DOMContentLoaded', () => {
    // Riferimenti agli elementi del DOM
    const launcher = document.getElementById('chatbot-launcher');
    const chatWindow = document.getElementById('chatbot-window');
    const closeBtn = document.getElementById('close-chatbot');
    const chatBox = document.getElementById('chat-box');
    const chatForm = document.getElementById('chat-form');
    const userInput = document.getElementById('user-input');
    const sendBtn = document.getElementById('send-btn');
    const modal = document.getElementById('product-modal');
    const productForm = document.getElementById('product-form');
    const productCodeInput = document.getElementById('product-code-input');
    const modalStatus = document.getElementById('modal-status');

    let currentProductCode = null;
    let chatHistory = [];

    // --- Funzioni di gestione della UI del widget ---

    // Mostra/Nasconde la finestra della chat
    launcher.addEventListener('click', async () => {
        chatWindow.classList.toggle('hidden');
        if (!currentProductCode) {
            // Se non c'è un prodotto corrente, prova a inizializzare con quello predefinito
            const config = await loadConfig();
            if (config.default_product_code) {
                const success = await initializeWithDefaultProduct(config.default_product_code);
                if (!success) {
                    modal.style.display = 'flex';
                    productCodeInput.focus();
                }
            } else {
                modal.style.display = 'flex';
                productCodeInput.focus();
            }
        }
    });

    closeBtn.addEventListener('click', () => {
        chatWindow.classList.add('hidden');
    });

    // --- Funzioni di gestione della chat ---

    // Aggiunge un messaggio alla chat-box
    function addMessage(text, sender) {
        const messageElement = document.createElement('div');
        messageElement.classList.add('message', sender === 'user' ? 'user-message' : 'bot-message');
        
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = text;

        // I link verranno gestiti dalla modale fullscreen
        // Non è più necessario aggiungere target="_blank"

        messageElement.innerHTML = tempDiv.innerHTML;
        chatBox.appendChild(messageElement);
        chatBox.scrollTop = chatBox.scrollHeight;
    }

    // Gestisce l'invio del codice prodotto
    productForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const productCode = productCodeInput.value.trim();
        if (!productCode) return;

        modalStatus.textContent = 'Preparazione dei documenti...';
        modalStatus.className = 'modal-status';

        try {
            const response = await fetch('/prepare', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ product_code: productCode }),
            });

            const data = await response.json();

            if (data.success) {
                currentProductCode = productCode;
                modalStatus.textContent = `Documenti pronti per ${productCode}!`;
                modalStatus.classList.add('success');
                setTimeout(() => {
                    modal.style.display = 'none';
                    userInput.disabled = false;
                    sendBtn.disabled = false;
                    userInput.focus();
                    addMessage(`Ciao! Sono pronto a rispondere a domande su <strong>${currentProductCode}</strong>.`, 'bot');
                }, 1500);
            } else {
                modalStatus.textContent = data.message;
                modalStatus.classList.add('error');
            }
        } catch (error) {
            modalStatus.textContent = 'Errore di connessione.';
            modalStatus.classList.add('error');
        }
    });

    // Gestisce l'invio di un messaggio di chat
    chatForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const userMessage = userInput.value.trim();
        if (!userMessage || !currentProductCode) return;

        addMessage(userMessage, 'user');
        userInput.value = '';
        userInput.disabled = true;
        sendBtn.disabled = true;

        addMessage('...', 'bot');

        try {
            const response = await fetch('/chat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    message: userMessage,
                    product_code: currentProductCode,
                    history: chatHistory
                }),
            });

            const data = await response.json();
            chatBox.removeChild(chatBox.lastChild);
            addMessage(data.answer, 'bot');
            chatHistory = data.history; // Aggiorna la cronologia

        } catch (error) {
            chatBox.removeChild(chatBox.lastChild);
            addMessage('Errore di connessione.', 'bot');
        } finally {
            userInput.disabled = false;
            sendBtn.disabled = false;
            userInput.focus();
        }
    });

    // --- Funzioni per la gestione del prodotto predefinito ---

    async function loadConfig() {
        try {
            const response = await fetch('/config');
            const config = await response.json();
            return config;
        } catch (error) {
            console.error('Errore nel caricamento della configurazione:', error);
            return {};
        }
    }

    async function initializeWithDefaultProduct(productCode) {
        if (!productCode) return false;

        modalStatus.textContent = 'Preparazione dei documenti...';
        modalStatus.className = 'modal-status';

        try {
            const response = await fetch('/prepare', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ product_code: productCode }),
            });

            const data = await response.json();

            if (data.success) {
                currentProductCode = productCode;
                modal.style.display = 'none';
                userInput.disabled = false;
                sendBtn.disabled = false;
                userInput.focus();
                addMessage('Sono il tuo assistente dedicato. Chiedimi informazioni e sarò lieto di aiutarti! 😊', 'bot');
                return true;
            } else {
                modalStatus.textContent = data.message || 'Errore nella preparazione dei documenti.';
                modalStatus.classList.add('error');
                return false;
            }
        } catch (error) {
            modalStatus.textContent = 'Errore di connessione con il server.';
            modalStatus.classList.add('error');
            return false;
        }
    }

    async function initializeApp() {
        const config = await loadConfig();

        if (config.default_product_code) {
            // Se c'è un prodotto predefinito, prova ad inizializzarlo automaticamente
            const success = await initializeWithDefaultProduct(config.default_product_code);
            if (!success) {
                // Se l'inizializzazione automatica fallisce, mostra il modale quando necessario
                // (il modale verrà mostrato quando si clicca sul launcher se currentProductCode è null)
            }
        }
        // Se non c'è un prodotto predefinito, il modale verrà mostrato quando si clicca sul launcher
    }

    // Inizializza l'applicazione
    initializeApp();
});