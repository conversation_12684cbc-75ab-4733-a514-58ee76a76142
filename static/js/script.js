document.addEventListener('DOMContentLoaded', () => {
    // Riferimenti agli elementi del DOM
    const chatBox = document.getElementById('chat-box');
    const chatForm = document.getElementById('chat-form');
    const userInput = document.getElementById('user-input');
    const sendBtn = document.getElementById('send-btn');
    const modal = document.getElementById('product-modal');
    const productForm = document.getElementById('product-form');
    const productCodeInput = document.getElementById('product-code-input');
    const modalStatus = document.getElementById('modal-status');
    const menuBtn = document.getElementById('menu-btn');
    const dropdownMenu = document.getElementById('dropdown-menu');
    const infoBtn = document.getElementById('info-btn');
    const clearChatBtn = document.getElementById('clear-chat-btn');
    const restartChatBtn = document.getElementById('restart-chat-btn');

    let currentProductCode = null;
    let chatHistory = []; // Aggiungi questa linea per la cronologia

    // --- Funzioni di gestione della UI ---

    // Mostra/Nasconde il menu a tendina
    menuBtn.addEventListener('click', () => {
        dropdownMenu.classList.toggle('show');
    });

    // Chiude il menu se si clicca fuori
    window.addEventListener('click', (e) => {
        if (!menuBtn.contains(e.target)) {
            dropdownMenu.classList.remove('show');
        }
    });

    // Funzione per mostrare le informazioni del prodotto
    infoBtn.addEventListener('click', (e) => {
        e.preventDefault();
        if (currentProductCode) {
            addMessage(`Prodotto corrente: <strong>${currentProductCode}</strong>`, 'bot');
        } else {
            addMessage('Nessun prodotto selezionato.', 'bot');
        }
        dropdownMenu.classList.remove('show');
    });

    // Funzione per svuotare la chat
    clearChatBtn.addEventListener('click', async (e) => {
        e.preventDefault();
        chatBox.innerHTML = ''; // Svuota la chat box
        chatHistory = []; // Svuota anche la cronologia
        addMessage('Chat cancellata. Pronto per una nuova conversazione! 😊', 'bot');
        dropdownMenu.classList.remove('show');
    });

    // Funzione per riavviare la chat
    restartChatBtn.addEventListener('click', async (e) => {
        e.preventDefault();
        // Resetta lo stato
        currentProductCode = null;
        chatHistory = []; // Resetta anche la cronologia
        chatBox.innerHTML = '';
        userInput.value = '';
        userInput.disabled = true;
        sendBtn.disabled = true;
        productCodeInput.value = '';
        modalStatus.textContent = '';
        modalStatus.className = 'modal-status';
        dropdownMenu.classList.remove('show');

        // Reinizializza l'applicazione (controlla il prodotto predefinito)
        await initializeApp();
    });


    // Aggiunge un messaggio alla chat-box
    function addMessage(text, sender) {
        const messageElement = document.createElement('div');
        messageElement.classList.add('message', sender);

        // Crea un elemento temporaneo per manipolare l'HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = text;

        // I link verranno gestiti dalla modale fullscreen
        // Non è più necessario aggiungere target="_blank"

        messageElement.innerHTML = tempDiv.innerHTML; // Usa l'HTML modificato
        chatBox.appendChild(messageElement);
        chatBox.scrollTop = chatBox.scrollHeight;
        return messageElement;
    }

    function addProgressMessage() {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message bot progress-message';
        messageDiv.innerHTML = `
            <div class="progress-container">
                <div class="progress-text">🔍 Elaborazione in corso...</div>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <div class="progress-steps">
                    <span class="step active">🔍 Ricerca</span>
                    <span class="step">🧠 Elaborazione</span>
                    <span class="step">✍️ Risposta</span>
                </div>
                <div class="progress-timer">
                    <span class="timer-text">Tempo trascorso: <span class="elapsed-time">0s</span></span>
                </div>
            </div>
        `;
        chatBox.appendChild(messageDiv);
        chatBox.scrollTop = chatBox.scrollHeight;

        // Timing semplificati per 3 passaggi essenziali
        const stepTimings = [
            { duration: 3000, text: "🔍 Ricerca nei documenti..." },
            { duration: 20000, text: "🧠 Elaborazione e analisi..." },
            { duration: 7000, text: "✍️ Generazione risposta..." }
        ];

        let currentStep = 0;
        let totalElapsed = 0;
        const steps = messageDiv.querySelectorAll('.step');
        const progressFill = messageDiv.querySelector('.progress-fill');
        const progressText = messageDiv.querySelector('.progress-text');
        const elapsedTimeSpan = messageDiv.querySelector('.elapsed-time');

        // Timer per il tempo trascorso
        const timerInterval = setInterval(() => {
            totalElapsed++;
            elapsedTimeSpan.textContent = `${totalElapsed}s`;
        }, 1000);

        // Funzione per avanzare al prossimo step
        function advanceStep() {
            if (currentStep < steps.length) {
                steps[currentStep].classList.add('active');
                progressText.textContent = stepTimings[currentStep].text;

                const progressPercent = ((currentStep + 1) / steps.length) * 100;
                progressFill.style.width = `${progressPercent}%`;

                currentStep++;

                if (currentStep < stepTimings.length) {
                    setTimeout(advanceStep, stepTimings[currentStep - 1].duration);
                }
            }
        }

        // Inizia il progresso dopo 500ms
        setTimeout(advanceStep, 500);

        // Salva gli intervalli per poterli cancellare
        messageDiv.progressInterval = timerInterval;
        messageDiv.stepAdvancer = advanceStep;

        return messageDiv;
    }

    function removeProgressMessage(messageDiv) {
        if (messageDiv && messageDiv.progressInterval) {
            clearInterval(messageDiv.progressInterval);
        }
        if (messageDiv && messageDiv.stepAdvancer) {
            // Cancella eventuali timeout pendenti
            clearTimeout(messageDiv.stepAdvancer);
        }
        if (messageDiv && messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }

    // Gestisce l'invio del codice prodotto
    productForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const productCode = productCodeInput.value.trim();
        if (!productCode) return;

        modalStatus.textContent = 'Preparazione dei documenti in corso...';
        modalStatus.className = 'modal-status';

        try {
            const response = await fetch('/prepare', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ product_code: productCode }),
            });

            const data = await response.json();

            if (data.success) {
                currentProductCode = productCode;
                modalStatus.textContent = `Documenti pronti per ${productCode}!`;
                modalStatus.classList.add('success');
                setTimeout(() => {
                    modal.style.display = 'none';
                    userInput.disabled = false;
                    sendBtn.disabled = false;
                    userInput.focus();
                    addMessage(`Ciao! Sono pronto a rispondere a domande sui documenti del prodotto <strong>${currentProductCode}</strong>.`, 'bot');
                }, 1500);
            } else {
                modalStatus.textContent = data.message;
                modalStatus.classList.add('error');
            }
        } catch (error) {
            modalStatus.textContent = 'Errore di connessione con il server.';
            modalStatus.classList.add('error');
        }
    });

    // Gestisce l'invio di un messaggio di chat
    chatForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const userMessage = userInput.value.trim();
        if (!userMessage || !currentProductCode) return;

        addMessage(userMessage, 'user');
        userInput.value = '';
        userInput.disabled = true;
        sendBtn.disabled = true;
        
        // Messaggio di attesa del bot con indicatore di progresso
        const loadingMessageId = addProgressMessage();

        try {
            const response = await fetch('/chat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    message: userMessage, 
                    product_code: currentProductCode,
                    history: chatHistory // Invia la cronologia
                }),
            });

            const data = await response.json();
            
            // Aggiorna la cronologia con la risposta del bot
            chatHistory = data.history;

            // Rimuove il messaggio di progresso
            removeProgressMessage(loadingMessageId);

            // Aggiunge la risposta vera e propria
            addMessage(data.answer, 'bot');

        } catch (error) {
            removeProgressMessage(loadingMessageId);
            addMessage('Si è verificato un errore di connessione.', 'bot');
        } finally {
            userInput.disabled = false;
            sendBtn.disabled = false;
            userInput.focus();
        }
    });

    // --- Funzioni per la gestione del prodotto predefinito ---

    async function loadConfig() {
        try {
            const response = await fetch('/config');
            const config = await response.json();
            return config;
        } catch (error) {
            console.error('Errore nel caricamento della configurazione:', error);
            return {};
        }
    }

    async function initializeWithDefaultProduct(productCode) {
        if (!productCode) return false;

        modalStatus.textContent = 'Preparazione dei documenti in corso...';
        modalStatus.className = 'modal-status';

        try {
            const response = await fetch('/prepare', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ product_code: productCode }),
            });

            const data = await response.json();

            if (data.success) {
                currentProductCode = productCode;
                modal.style.display = 'none';
                userInput.disabled = false;
                sendBtn.disabled = false;
                userInput.focus();
                addMessage('Sono il tuo assistente dedicato. Chiedimi informazioni e sarò lieto di aiutarti! 😊', 'bot');
                return true;
            } else {
                modalStatus.textContent = data.message || 'Errore nella preparazione dei documenti.';
                modalStatus.classList.add('error');
                return false;
            }
        } catch (error) {
            modalStatus.textContent = 'Errore di connessione con il server.';
            modalStatus.classList.add('error');
            return false;
        }
    }

    async function initializeApp() {
        const config = await loadConfig();

        if (config.default_product_code) {
            // Se c'è un prodotto predefinito, prova ad inizializzarlo automaticamente
            const success = await initializeWithDefaultProduct(config.default_product_code);
            if (!success) {
                // Se l'inizializzazione automatica fallisce, mostra il modale
                modal.style.display = 'flex';
                productCodeInput.focus();
            }
        } else {
            // Se non c'è un prodotto predefinito, mostra il modale
            modal.style.display = 'flex';
            productCodeInput.focus();
        }
    }

    // Inizializza l'applicazione
    initializeApp();
});