/**
 * Sistema di modale fullscreen per l'apertura dei link
 * Gestisce navigazione, cronologia, loader e gestione errori
 * VERSIONE MIGLIORATA PER PDF
 */

class LinkModal {
    constructor() {
        this.modal = null;
        this.iframe = null;
        this.loader = null;
        this.errorContainer = null;
        this.titleElement = null;
        this.backBtn = null;
        this.forwardBtn = null;
        this.refreshBtn = null;
        this.closeBtn = null;
        this.retryBtn = null;
        this.overlay = null;
        
        // Cronologia di navigazione
        this.history = [];
        this.currentIndex = -1;
        this.currentUrl = '';
        
        // Timeout per il caricamento
        this.loadTimeout = null;
        this.maxLoadTime = 20000; // Aumentato a 20 secondi per PDF
        this.isLoaded = false;
        this.isPdfLoading = false; // Flag specifico per PDF
        
        this.init();
    }
    
    init() {
        this.modal = document.getElementById('link-modal');
        this.iframe = document.getElementById('modal-iframe');
        this.loader = document.getElementById('modal-loader');
        this.errorContainer = document.getElementById('modal-error');
        this.titleElement = document.getElementById('modal-title');
        this.backBtn = document.getElementById('modal-back-btn');
        this.forwardBtn = document.getElementById('modal-forward-btn');
        this.refreshBtn = document.getElementById('modal-refresh-btn');
        this.closeBtn = document.getElementById('modal-close-btn');
        this.retryBtn = document.getElementById('modal-retry-btn');
        this.overlay = this.modal?.querySelector('.modal-overlay');
        
        if (!this.modal) {
            console.warn('Link modal elements not found');
            return;
        }
        
        this.bindEvents();
    }
    
    bindEvents() {
        // Eventi dei pulsanti
        this.closeBtn?.addEventListener('click', () => this.close());
        this.backBtn?.addEventListener('click', () => this.goBack());
        this.forwardBtn?.addEventListener('click', () => this.goForward());
        this.refreshBtn?.addEventListener('click', () => this.refresh());
        this.retryBtn?.addEventListener('click', () => this.retry());
        
        // Chiusura con click sull'overlay
        this.overlay?.addEventListener('click', () => this.close());
        
        // Gestione tasti di scelta rapida e focus trap
        document.addEventListener('keydown', (e) => {
            if (!this.isOpen()) return;

            if (e.key === 'Tab') {
                this.handleFocusTrap(e);
            }

            switch(e.key) {
                case 'Escape':
                    this.close();
                    break;
                case 'F5':
                    e.preventDefault();
                    this.refresh();
                    break;
                case 'ArrowLeft':
                    if (e.altKey) {
                        e.preventDefault();
                        this.goBack();
                    }
                    break;
                case 'ArrowRight':
                    if (e.altKey) {
                        e.preventDefault();
                        this.goForward();
                    }
                    break;
            }
        });
        
        // Eventi dell'iframe/object
        if (this.iframe) {
            this.iframe.addEventListener('load', () => this.onIframeLoad());
            this.iframe.addEventListener('error', () => {
                // Non gestire errori per PDF - spesso sono falsi positivi
                if (!this.isPdfUrl(this.currentUrl)) {
                    this.onIframeError();
                }
            });
        }
        
        // Intercetta i click sui link nei messaggi
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');

            if (link && link.href) {
                const chatBox = document.getElementById('chat-box');
                if (chatBox && chatBox.contains(link)) {
                    e.preventDefault();
                    this.openLink(link.href, link.textContent || link.href);
                }
            }
        });
    }
    
    openLink(url, title = '') {
        if (!url || !this.modal) return;

        console.log('Apertura link:', url);

        this.currentUrl = url;
        this.showModal();
        this.showLoader();
        this.hideError();
        this.hideIframe();

        const isPdf = this.isPdfUrl(url);
        const displayTitle = title || (isPdf ? 'Caricamento PDF...' : 'Caricamento...');
        this.updateTitle(displayTitle);

        if (this.currentIndex === -1 || this.history[this.currentIndex] !== url) {
            this.history = this.history.slice(0, this.currentIndex + 1);
            this.history.push(url);
            this.currentIndex = this.history.length - 1;
        }

        this.updateNavigationButtons();
        this.loadUrl(url);
    }
    
    isPdfUrl(url) {
        const lowercaseUrl = url.toLowerCase();
        return lowercaseUrl.includes('.pdf') || lowercaseUrl.includes('pdf');
    }
    
    loadUrl(url) {
        if (!this.iframe) return;

        this.clearLoadTimeout();
        this.isLoaded = false;
        this.isPdfLoading = this.isPdfUrl(url);

        if (!this.isValidUrl(url)) {
            this.onIframeError('URL non valido');
            return;
        }

        // Timeout più lungo per PDF
        const timeoutDuration = this.isPdfLoading ? 30000 : this.maxLoadTime;
        this.loadTimeout = setTimeout(() => {
            this.onLoadTimeout();
        }, timeoutDuration);

        try {
            const isPdf = this.isPdfUrl(url);
            console.log(`Caricamento ${isPdf ? 'PDF' : 'pagina'}:`, url);

            if (isPdf) {
                // Per PDF, usa diverse strategie basate sul tipo di elemento
                this.loadPdfContent(url);
            } else {
                // Per contenuti normali
                if (this.iframe.tagName.toLowerCase() === 'object') {
                    this.iframe.data = url;
                } else {
                    this.iframe.src = url;
                }
            }

            // Verifica periodica del caricamento per PDF
            if (isPdf) {
                this.startPdfLoadCheck();
            }

        } catch (error) {
            console.error('Errore nel caricamento:', error);
            this.onIframeError('Errore nel caricamento del link: ' + error.message);
        }
    }

    loadPdfContent(url) {
        // Assicurati che l'URL sia completo
        const fullUrl = url.startsWith('http') ? url : window.location.origin + url;
        
        console.log('Caricamento PDF con URL completo:', fullUrl);
        
        // Diverse strategie per diversi tipi di elementi
        if (this.iframe.tagName.toLowerCase() === 'object') {
            // Se è un elemento object
            this.iframe.data = fullUrl;
            this.iframe.type = 'application/pdf';
        } else if (this.iframe.tagName.toLowerCase() === 'embed') {
            // Se è un elemento embed
            this.iframe.src = fullUrl;
            this.iframe.type = 'application/pdf';
        } else {
            // Se è un iframe, prova con Google PDF Viewer come fallback
            this.iframe.src = fullUrl;
            
            // Fallback: se il caricamento diretto fallisce, prova con Google PDF Viewer
            setTimeout(() => {
                if (!this.isLoaded && this.isPdfLoading) {
                    console.log('Tentativo fallback con Google PDF Viewer');
                    this.iframe.src = `https://docs.google.com/gview?url=${encodeURIComponent(fullUrl)}&embedded=true`;
                }
            }, 10000); // Prova il fallback dopo 10 secondi
        }
    }

    startPdfLoadCheck() {
        // Controlla periodicamente se il PDF si è caricato
        let checks = 0;
        const maxChecks = 15; // 15 controlli = 30 secondi con intervalli di 2 secondi
        
        const checkInterval = setInterval(() => {
            checks++;
            
            // Se è già caricato, ferma il controllo
            if (this.isLoaded || !this.isPdfLoading || !this.isOpen()) {
                clearInterval(checkInterval);
                return;
            }
            
            // Prova a verificare se il contenuto è disponibile
            if (this.iframe.tagName.toLowerCase() === 'object') {
                try {
                    // Per object, verifica se il contenuto è disponibile
                    if (this.iframe.contentDocument || this.iframe.data) {
                        console.log('PDF verificato con successo via object');
                        this.onIframeLoad();
                        clearInterval(checkInterval);
                        return;
                    }
                } catch (e) {
                    // Normale per PDF - non possiamo accedere al contenuto
                }
            }
            
            // Se abbiamo fatto troppi controlli, consideriamo il PDF caricato
            // (molti PDF non triggerano l'evento load)
            if (checks >= maxChecks) {
                console.log('PDF considerato caricato dopo controlli periodici');
                this.onIframeLoad();
                clearInterval(checkInterval);
            }
        }, 2000);
    }

    isValidUrl(string) {
        if (string.startsWith('/')) {
            return true;
        }
        
        try {
            const url = new URL(string);
            return url.protocol === 'http:' || url.protocol === 'https:';
        } catch (_) {
            return false;
        }
    }
    
    onIframeLoad() {
        this.clearLoadTimeout();

        if (!this.currentUrl || this.iframe.src?.includes('about:blank') || this.iframe.data?.includes('about:blank')) {
            return;
        }

        console.log('Evento load ricevuto per:', this.currentUrl);

        const isPdf = this.isPdfUrl(this.currentUrl);

        if (isPdf) {
            console.log('PDF caricato con successo');
            this.isPdfLoading = false;
            this.isLoaded = true;
            this.hideLoader();
            this.hideError();
            this.showIframe();
            this.updateTitle('PDF caricato');
            return;
        }

        // Gestione per contenuti normali
        try {
            const iframeDoc = this.iframe.contentDocument || this.iframe.contentWindow?.document;

            if (iframeDoc && iframeDoc.body) {
                const bodyText = iframeDoc.body.textContent || '';

                if (bodyText.toLowerCase().includes('404') ||
                    bodyText.toLowerCase().includes('not found') ||
                    bodyText.toLowerCase().includes('page not found')) {
                    this.onIframeError('Pagina non trovata (404)');
                    return;
                }

                const pageTitle = iframeDoc.title;
                if (pageTitle && pageTitle.trim()) {
                    this.updateTitle(pageTitle);
                }
            }
        } catch (error) {
            console.log('CORS restriction - cannot access content:', error.message);
        }

        this.isLoaded = true;
        this.hideLoader();
        this.hideError();
        this.showIframe();
    }
    
    onIframeError(message = 'Errore di caricamento') {
        this.clearLoadTimeout();
        this.hideLoader();
        this.hideIframe();
        this.isPdfLoading = false;

        console.log('Errore di caricamento:', message, 'URL:', this.currentUrl);

        const isPdf = this.isPdfUrl(this.currentUrl);
        let errorMessage = message;
        let suggestions = '';

        if (isPdf) {
            errorMessage = 'Errore di caricamento del PDF';
            suggestions = 'Il file PDF potrebbe essere protetto, danneggiato o il server potrebbe non permettere la visualizzazione in iframe. ' +
                          'Prova ad aprirlo in una nuova scheda o a scaricarlo direttamente.';
        } else if (message.includes('Timeout')) {
            suggestions = 'La pagina potrebbe essere lenta o non disponibile. Puoi provare ad aprirla in una nuova scheda.';
        } else if (message.includes('404') || message.includes('not found')) {
            suggestions = 'La pagina richiesta non esiste o è stata spostata.';
        } else if (message.includes('URL non valido')) {
            suggestions = 'L\'indirizzo fornito non è corretto.';
        } else {
            suggestions = 'Il sito potrebbe bloccare la visualizzazione in iframe per motivi di sicurezza. Puoi provare ad aprirlo in una nuova scheda.';
        }

        this.showError(errorMessage, suggestions);
        this.addOpenInNewTabButton();
    }

    onLoadTimeout() {
        if (this.isLoaded || !this.isOpen()) {
            return;
        }

        console.warn(`Timeout caricamento per ${this.currentUrl}`);
        
        // Se è un PDF, consideralo caricato e mostra l'iframe
        if (this.isPdfLoading) {
            console.log('Timeout per PDF, mostro comunque il contenuto.');
            this.isLoaded = true;
            this.isPdfLoading = false;
            this.hideLoader();
            this.hideError();
            this.showIframe();
            this.updateTitle('PDF Caricato');
        } else {
            this.onIframeError('Timeout: il caricamento della pagina ha richiesto troppo tempo');
        }
    }
    
    goBack() {
        if (this.currentIndex > 0) {
            this.currentIndex--;
            const url = this.history[this.currentIndex];
            this.currentUrl = url;
            this.showLoader();
            this.hideError();
            this.hideIframe();
            this.updateNavigationButtons();
            this.loadUrl(url);
        }
    }
    
    goForward() {
        if (this.currentIndex < this.history.length - 1) {
            this.currentIndex++;
            const url = this.history[this.currentIndex];
            this.currentUrl = url;
            this.showLoader();
            this.hideError();
            this.hideIframe();
            this.updateNavigationButtons();
            this.loadUrl(url);
        }
    }
    
    refresh() {
        if (this.currentUrl) {
            this.showLoader();
            this.hideError();
            this.hideIframe();
            this.loadUrl(this.currentUrl);
        }
    }
    
    retry() {
        this.refresh();
    }
    
    close() {
        if (this.modal) {
            this.modal.classList.add('hidden');
            this.clearLoadTimeout();

            if (this.previousFocus) {
                this.previousFocus.focus();
                this.previousFocus = null;
            }

            document.body.style.overflow = '';

            setTimeout(() => {
                if (this.iframe) {
                    if (this.iframe.tagName.toLowerCase() === 'object') {
                        this.iframe.data = 'about:blank';
                    } else {
                        this.iframe.src = 'about:blank';
                    }
                }
                this.hideLoader();
                this.hideError();
                this.hideIframe();
                this.updateTitle('');
                this.isLoaded = false;
                this.isPdfLoading = false;

                this.history = [];
                this.currentIndex = -1;
                this.currentUrl = null;
                this.updateNavigationButtons();
            }, 300);
        }
    }
    
    showModal() {
        if (this.modal) {
            this.modal.classList.remove('hidden');

            this.previousFocus = document.activeElement;

            setTimeout(() => {
                if (this.closeBtn) {
                    this.closeBtn.focus();
                }
            }, 100);

            document.body.style.overflow = 'hidden';
        }
    }
    
    isOpen() {
        return this.modal && !this.modal.classList.contains('hidden');
    }
    
    showLoader() {
        if (this.loader) {
            this.loader.classList.remove('hidden');

            const loaderText = this.loader.querySelector('.loader-text');
            if (loaderText) {
                const message = this.isPdfLoading ? 'Caricamento PDF' : 'Caricamento in corso';
                loaderText.textContent = message;

                let dots = 0;
                const loadingInterval = setInterval(() => {
                    if (this.loader.classList.contains('hidden')) {
                        clearInterval(loadingInterval);
                        return;
                    }

                    dots = (dots + 1) % 4;
                    loaderText.textContent = message + '.'.repeat(dots);
                }, 500);
            }
        }
    }
    
    hideLoader() {
        if (this.loader) {
            this.loader.classList.add('hidden');
        }
    }
    
    showIframe() {
        if (this.iframe) {
            this.iframe.classList.remove('hidden');
        }
    }
    
    hideIframe() {
        if (this.iframe) {
            this.iframe.classList.add('hidden');
        }
    }
    
    showError(message, suggestions = '') {
        if (this.errorContainer) {
            this.errorContainer.classList.remove('hidden');
            const messageElement = document.getElementById('modal-error-message');
            if (messageElement) {
                let fullMessage = message;
                if (suggestions) {
                    fullMessage += '\n\n' + suggestions;
                }
                messageElement.textContent = fullMessage;
            }
        }
    }
    
    hideError() {
        if (this.errorContainer) {
            this.errorContainer.classList.add('hidden');

            const newTabBtn = this.errorContainer.querySelector('.open-new-tab-btn');
            if (newTabBtn) {
                newTabBtn.remove();
            }
        }
    }
    
    updateTitle(title) {
        if (this.titleElement) {
            this.titleElement.textContent = title;
        }
    }
    
    updateNavigationButtons() {
        if (this.backBtn) {
            this.backBtn.disabled = this.currentIndex <= 0;
        }
        if (this.forwardBtn) {
            this.forwardBtn.disabled = this.currentIndex >= this.history.length - 1;
        }
    }
    
    clearLoadTimeout() {
        if (this.loadTimeout) {
            clearTimeout(this.loadTimeout);
            this.loadTimeout = null;
        }
    }

    addOpenInNewTabButton() {
        const errorContainer = this.errorContainer;
        if (!errorContainer || !this.currentUrl) return;

        const existingBtn = errorContainer.querySelector('.open-new-tab-btn');
        if (existingBtn) {
            existingBtn.remove();
        }

        const newTabBtn = document.createElement('button');
        newTabBtn.className = 'open-new-tab-btn';
        newTabBtn.textContent = 'Apri in nuova scheda';
        newTabBtn.style.cssText = `
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin-left: 10px;
            transition: background-color 0.2s ease;
        `;

        newTabBtn.addEventListener('click', () => {
            window.open(this.currentUrl, '_blank');
            this.close();
        });

        newTabBtn.addEventListener('mouseenter', () => {
            newTabBtn.style.backgroundColor = '#0056b3';
        });

        newTabBtn.addEventListener('mouseleave', () => {
            newTabBtn.style.backgroundColor = '#007bff';
        });

        const retryBtn = errorContainer.querySelector('.retry-btn');
        if (retryBtn && retryBtn.parentNode) {
            retryBtn.parentNode.insertBefore(newTabBtn, retryBtn.nextSibling);
        }
    }

    handleFocusTrap(e) {
        if (!this.modal) return;

        const focusableElements = this.modal.querySelectorAll(
            'button:not([disabled]), [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        const firstFocusable = focusableElements[0];
        const lastFocusable = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
            if (document.activeElement === firstFocusable) {
                e.preventDefault();
                lastFocusable.focus();
            }
        } else {
            if (document.activeElement === lastFocusable) {
                e.preventDefault();
                firstFocusable.focus();
            }
        }
    }
}

// Inizializza la modale quando il DOM è pronto
document.addEventListener('DOMContentLoaded', () => {
    window.linkModal = new LinkModal();
});