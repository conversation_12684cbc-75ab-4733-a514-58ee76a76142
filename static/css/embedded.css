/* Stili generali */
body {
    font-family: 'Roboto', sans-serif;
    margin: 0;
    padding: 0;
}

/* Widget del chatbot */
#chatbot-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

/* Pulsante di avvio */
#chatbot-launcher {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.3s;
}

#chatbot-launcher:hover {
    background-color: #0056b3;
}

/* Finestra del chatbot */
#chatbot-window {
    width: 350px;
    height: 500px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: transform 0.3s ease-out, opacity 0.3s ease-out;
    transform-origin: bottom right;
}

#chatbot-window.hidden {
    transform: scale(0);
    opacity: 0;
    display: none;
}

/* Header del chat */
.chat-header {
    background-color: #007bff;
    color: white;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h2 {
    margin: 0;
    font-size: 1.1em;
}

#close-chatbot {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
}

/* Box della chat */
.chat-box {
    flex-grow: 1;
    padding: 15px;
    overflow-y: auto;
    background-color: #f9f9f9;
}

/* Area di input */
.chat-input-area {
    padding: 10px;
    border-top: 1px solid #ddd;
}

.chat-form {
    display: flex;
}

#user-input {
    flex-grow: 1;
    border: 1px solid #ccc;
    border-radius: 20px;
    padding: 10px 15px;
    font-size: 1em;
}

#send-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0 10px;
    color: #007bff;
}

/* Stili dei messaggi */
.message {
    margin-bottom: 15px;
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 80%;
    line-height: 1.4;
}

.user-message {
    background-color: #007bff;
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: 4px;
}

.bot-message {
    background-color: #e9ecef;
    color: #333;
    align-self: flex-start;
    border-bottom-left-radius: 4px;
}

/* Stili per il contenuto HTML generato dal bot */
.bot-message p {
    margin: 0;
}
.bot-message a {
    color: #005cbf;
    text-decoration: none;
    font-weight: 500;
}
.bot-message a:hover {
    text-decoration: underline;
}

/* Stili per elenchi puntati e numerati nelle risposte del bot */
.bot-message ul,
.bot-message ol {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
    line-height: 1.6;
}

.bot-message ul li,
.bot-message ol li {
    margin: 0.3rem 0;
    padding-left: 0.2rem;
}

.bot-message ul {
    list-style-type: disc;
}

.bot-message ol {
    list-style-type: decimal;
}

/* Stili per elenchi annidati */
.bot-message ul ul,
.bot-message ol ol,
.bot-message ul ol,
.bot-message ol ul {
    margin: 0.2rem 0;
    padding-left: 1.2rem;
}

.bot-message ul ul {
    list-style-type: circle;
}

.bot-message ul ul ul {
    list-style-type: square;
}

/* Modal */
.modal {
    display: none; 
    position: fixed; 
    z-index: 1001; 
    left: 0;
    top: 0;
    width: 100%; 
    height: 100%; 
    overflow: auto; 
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 400px;
    border-radius: 10px;
    text-align: center;
}

/* Stili per la modale fullscreen dei link */
.link-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.link-modal.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
}

.modal-container {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: white;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transform: scale(1);
    transition: transform 0.3s ease;
}

.link-modal.hidden .modal-container {
    transform: scale(0.9);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background-color: #f8f9fa;
    color: #333;
    border-bottom: 1px solid #dee2e6;
    min-height: 60px;
}

.modal-nav-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.modal-nav-btn {
    background: white;
    border: 1px solid #dee2e6;
    color: #333;
    border-radius: 6px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.modal-nav-btn:hover:not(:disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
}

.modal-nav-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.modal-title {
    flex: 1;
    text-align: center;
    font-weight: 500;
    font-size: 16px;
    margin: 0 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #333;
}

.modal-title h1 {
    margin: 0;
    font-size: inherit;
    font-weight: inherit;
    color: #333;
}

.modal-close-btn {
    background: white;
    border: 1px solid #dee2e6;
    color: #333;
    border-radius: 6px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.modal-close-btn:hover {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
}

.modal-content-area {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: #f8f9fa;
}



.modal-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background-color: white;
    overflow: hidden;
}

/* Nasconde i controlli del PDF viewer quando possibile */
.modal-iframe::-webkit-scrollbar {
    display: none;
}

.modal-iframe {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.modal-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    z-index: 10;
}

.loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e3e3e3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loader-text {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.modal-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    text-align: center;
    padding: 40px;
    z-index: 10;
}

.error-icon {
    font-size: 48px;
}

.error-title {
    font-size: 20px;
    font-weight: 600;
    color: #dc3545;
    margin: 0;
}

.error-title h2 {
    margin: 0;
    font-size: inherit;
    font-weight: inherit;
    color: inherit;
}

.error-message {
    font-size: 14px;
    color: #666;
    margin: 0;
    max-width: 400px;
}

.retry-btn {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.retry-btn:hover {
    background-color: #0056b3;
}

/* Responsive design per la modale */
@media (max-width: 768px) {

    .modal-header {
        padding: 12px 16px;
        min-height: 56px;
    }

    .modal-nav-btn {
        width: 32px;
        height: 32px;
        padding: 6px;
    }

    .modal-close-btn {
        width: 36px;
        height: 36px;
        padding: 6px;
    }

    .modal-title {
        font-size: 14px;
        margin: 0 12px;
    }

    .modal-error {
        padding: 20px;
    }

    .error-title {
        font-size: 18px;
    }

    .loader-spinner {
        width: 32px;
        height: 32px;
    }

    .loader-text {
        font-size: 12px;
    }
}

/* Ottimizzazioni per schermi molto piccoli */
@media (max-width: 480px) {
    .modal-nav-controls {
        gap: 4px;
    }

    .modal-nav-btn {
        width: 28px;
        height: 28px;
        padding: 4px;
    }

    .modal-close-btn {
        width: 32px;
        height: 32px;
        padding: 4px;
    }

    .modal-title {
        font-size: 12px;
        margin: 0 8px;
        color: #333;
    }

    .modal-header {
        padding: 8px 12px;
        min-height: 48px;
    }

    .error-message {
        font-size: 12px;
    }

    .retry-btn {
        padding: 8px 16px;
        font-size: 12px;
    }
}
