# Configurazione Prodotto Predefinito

Questa funzionalità permette di configurare un prodotto predefinito che verrà automaticamente selezionato all'avvio della chat, evitando di dover inserire manualmente il codice prodotto ogni volta.

## Come Configurare

1. **Modifica il file `.env`**:
   ```env
   # Default Product Configuration
   # Se impostato, questo prodotto verrà selezionato automaticamente all'avvio della chat
   DEFAULT_PRODUCT_CODE=NOME_DEL_TUO_PRODOTTO
   ```

2. **Assicurati che il prodotto esista**:
   - Il prodotto deve avere una cartella corrispondente in `./pdf/NOME_DEL_TUO_PRODOTTO/`
   - La cartella deve contenere i documenti PDF o di testo da indicizzare

## Comportamento

### Con Prodotto Predefinito Configurato
- All'avvio della chat, il sistema tenterà automaticamente di inizializzare il prodotto configurato
- Se l'inizializzazione ha successo, l'utente verrà portato direttamente alla chat
- Se l'inizializzazione fallisce (es. prodotto non trovato), verrà mostrato il modale per la selezione manuale

### Senza Prodotto Predefinito
- Viene mostrato il modale "Codice Prodotto Richiesto" come comportamento standard
- L'utente deve inserire manualmente il codice prodotto

### Pulsante "Riavvia Chat"
- Resetta la sessione corrente
- Ricontrolla la configurazione del prodotto predefinito
- Se configurato, tenta l'inizializzazione automatica
- Altrimenti mostra il modale per la selezione

## Esempi di Configurazione

### Esempio 1: Prodotto Specifico
```env
DEFAULT_PRODUCT_CODE=ESEMPIO_PRODOTTO
```

### Esempio 2: Disabilitato (comportamento standard)
```env
# DEFAULT_PRODUCT_CODE=ESEMPIO_PRODOTTO
```

## Prodotti Disponibili nel Sistema

Basandosi sulla struttura attuale della cartella `pdf/`:
- `ESEMPIO_PRODOTTO`
- `QP`
- `SYM`

## Note Tecniche

- La configurazione viene letta dal server Flask all'avvio
- L'endpoint `/config` fornisce la configurazione al frontend
- Il JavaScript controlla automaticamente la presenza di un prodotto predefinito
- Funziona sia per la versione principale che per quella embedded del widget

## Test della Funzionalità

1. **Test senza prodotto predefinito**:
   - Commenta la riga `DEFAULT_PRODUCT_CODE` nel file `.env`
   - Riavvia il server
   - Apri la chat: dovrebbe apparire il modale

2. **Test con prodotto predefinito**:
   - Imposta `DEFAULT_PRODUCT_CODE=ESEMPIO_PRODOTTO` nel file `.env`
   - Riavvia il server
   - Apri la chat: dovrebbe inizializzare automaticamente il prodotto

3. **Test del pulsante "Riavvia Chat"**:
   - Con un prodotto predefinito configurato
   - Clicca su "Riavvia Chat"
   - Dovrebbe reinizializzare automaticamente lo stesso prodotto
