<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbot Embedded</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/embedded.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body>

    <div id="chatbot-widget">
        <button id="chatbot-launcher">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="32" height="32">
                <path d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zm0 1.5a8.25 8.25 0 100 16.5 8.25 8.25 0 000-16.5z" />
                <path d="M12 6.75a.75.75 0 01.75.75v3.75h3.75a.75.75 0 010 1.5h-3.75V16.5a.75.75 0 01-1.5 0v-3.75H7.5a.75.75 0 010-1.5h3.75V7.5a.75.75 0 01.75-.75z" />
            </svg>
        </button>

        <div id="chatbot-window" class="hidden">
            <div class="chat-header">
                <h2>Assistente Virtuale</h2>
                <button id="close-chatbot">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24" height="24">
                        <path fill-rule="evenodd" d="M5.47 5.47a.75.75 0 011.06 0L12 10.94l5.47-5.47a.75.75 0 111.06 1.06L13.06 12l5.47 5.47a.75.75 0 11-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 01-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 010-1.06z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
            <div class="chat-box" id="chat-box">
                </div>
            <div class="chat-input-area">
                <form id="chat-form" class="chat-form">
                    <input type="text" id="user-input" placeholder="Scrivi la tua domanda..." autocomplete="off" disabled>
                    <button type="submit" id="send-btn" disabled>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24" height="24"><path d="M3.478 2.405a.75.75 0 00-.926.94l2.432 7.905H13.5a.75.75 0 010 1.5H4.984l-2.432 7.905a.75.75 0 00.926.94 60.519 60.519 0 0018.445-8.986.75.75 0 000-1.218A60.517 60.517 0 003.478 2.405z" /></svg>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="modal" id="product-modal">
        <div class="modal-content">
            <h2>Codice Prodotto Richiesto</h2>
            <p>Per iniziare, inserisci il codice prodotto di cui vuoi consultare la documentazione.</p>
            <form id="product-form">
                <input type="text" id="product-code-input" placeholder="Es: CODICE_PRODOTTO_A" required>
                <button type="submit">Inizia a Chattare</button>
            </form>
            <div id="modal-status" class="modal-status"></div>
        </div>
    </div>

    <!-- Modale fullscreen per i link -->
    <div id="link-modal" class="link-modal hidden" role="dialog" aria-modal="true" aria-labelledby="modal-title">
        <div class="modal-overlay" aria-hidden="true"></div>
        <div class="modal-container">
            <!-- Header della modale -->
            <div class="modal-header">
                <div class="modal-nav-controls">
                    <button id="modal-back-btn" class="modal-nav-btn" title="Indietro (Alt+←)" aria-label="Vai alla pagina precedente" disabled>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                            <path d="m15 18-6-6 6-6"/>
                        </svg>
                    </button>
                    <button id="modal-forward-btn" class="modal-nav-btn" title="Avanti (Alt+→)" aria-label="Vai alla pagina successiva" disabled>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                            <path d="m9 18 6-6-6-6"/>
                        </svg>
                    </button>
                    <button id="modal-refresh-btn" class="modal-nav-btn" title="Ricarica (F5)" aria-label="Ricarica la pagina">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                            <path d="M21 3v5h-5"/>
                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                            <path d="M3 21v-5h5"/>
                        </svg>
                    </button>
                </div>
                <h1 class="modal-title" id="modal-title">Caricamento...</h1>
                <button id="modal-close-btn" class="modal-close-btn" title="Chiudi (Esc)" aria-label="Chiudi la modale">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                        <path d="m18 6-12 12"/>
                        <path d="m6 6 12 12"/>
                    </svg>
                </button>
            </div>

            <!-- Contenuto della modale -->
            <div class="modal-content-area" role="main">
                <!-- Loader -->
                <div id="modal-loader" class="modal-loader" aria-live="polite" aria-label="Caricamento in corso">
                </div>

                <!-- Messaggio di errore -->
                <div id="modal-error" class="modal-error hidden" role="alert" aria-live="assertive">
                </div>

                <!-- Oggetto per il contenuto PDF -->
                <object id="modal-iframe" class="modal-iframe hidden" type="application/pdf" data="about:blank">
                    <p>Il tuo browser non supporta la visualizzazione di PDF. Puoi scaricare il file per visualizzarlo.</p>
                </object>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/link-modal.js') }}"></script>
    <script src="{{ url_for('static', filename='js/embedded.js') }}"></script>
</body>
</html>