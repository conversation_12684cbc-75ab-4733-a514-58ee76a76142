#!/usr/bin/env python3
"""
Demo pratico del sistema di gestione delle fonti documentali.
Questo script dimostra come il sistema gestisce diversamente:
1. <PERSON>onti ufficiali (directory standard) - con citazione e link
2. Fonti di riferimento interno (directory nolink) - usate silenziosamente
"""

import os
import sys
from dotenv import load_dotenv

# Carica le variabili d'ambiente
load_dotenv()

# Aggiungi il percorso del progetto al PYTHONPATH
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pdf_chatbot_prodotti import ProductChatbot

def main():
    """Esegue una demo del sistema di gestione delle fonti."""
    
    # Verifica le API keys
    jina_api_key = os.getenv('JINA_API_KEY')
    gemini_api_key = os.getenv('GEMINI_API_KEY')
    
    if not jina_api_key or not gemini_api_key:
        print("❌ API keys non disponibili. Assicurati di aver configurato JINA_API_KEY e GEMINI_API_KEY nel file .env")
        return
    
    print("🚀 Demo Sistema di Gestione Fonti Documentali")
    print("=" * 60)
    
    # Inizializza il chatbot
    chatbot = ProductChatbot(jina_api_key, gemini_api_key)
    product_code = "ESEMPIO_PRODOTTO"
    
    print(f"📁 Preparazione documenti per prodotto: {product_code}")
    try:
        chatbot.prepare_product_documents(product_code)
        print("✅ Documenti preparati con successo")
    except Exception as e:
        print(f"❌ Errore nella preparazione dei documenti: {e}")
        return
    
    print("\n" + "=" * 60)
    print("📋 STRUTTURA DELLE FONTI:")
    print("📄 Fonti Ufficiali (con link):")
    print("   - pdf/ESEMPIO_PRODOTTO/manuale_utente.txt")
    print("   - pdf/ESEMPIO_PRODOTTO/specifiche_tecniche.md")
    print("\n🔒 Fonti di Riferimento Interno (senza citazione):")
    print("   - pdf/ESEMPIO_PRODOTTO/nolink/riferimento_interno.txt")
    
    print("\n" + "=" * 60)
    print("🧪 TEST DELLE QUERY:")
    
    # Test 1: Query che dovrebbe utilizzare fonti ufficiali
    print("\n1️⃣ Query su fonti ufficiali:")
    query1 = "Dimmi qualcosa sul manuale utente"
    print(f"   Query: {query1}")
    
    try:
        answer1, _, _ = chatbot.search_and_answer(query1, product_code)
        print(f"   Risposta: {answer1[:200]}...")
        
        # Verifica presenza di link
        import re
        links = re.findall(r'\[📄[^\]]+\]\([^)]+\)', answer1)
        if links:
            print(f"   ✅ Link trovati: {len(links)}")
            for link in links:
                print(f"      - {link}")
        else:
            print("   ⚠️ Nessun link trovato")
            
    except Exception as e:
        print(f"   ❌ Errore: {e}")
    
    # Test 2: Query generica che potrebbe utilizzare entrambi i tipi di fonti
    print("\n2️⃣ Query generica (potrebbe usare entrambi i tipi di fonti):")
    query2 = "Ci sono informazioni tecniche aggiuntive sul prodotto?"
    print(f"   Query: {query2}")
    
    try:
        answer2, _, _ = chatbot.search_and_answer(query2, product_code)
        print(f"   Risposta: {answer2[:300]}...")
        
        # Verifica che non ci siano riferimenti espliciti alle fonti nolink
        if "nolink" in answer2 or "riferimento_interno" in answer2:
            print("   ⚠️ ATTENZIONE: La risposta contiene riferimenti alle fonti nolink!")
        else:
            print("   ✅ Nessun riferimento esplicito alle fonti nolink")
            
        # Verifica presenza di link alle fonti ufficiali
        links = re.findall(r'\[📄[^\]]+\]\([^)]+\)', answer2)
        if links:
            print(f"   ✅ Link alle fonti ufficiali: {len(links)}")
        else:
            print("   ℹ️ Nessun link alle fonti ufficiali in questa risposta")
            
    except Exception as e:
        print(f"   ❌ Errore: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 RISULTATI:")
    print("✅ Il sistema distingue correttamente tra:")
    print("   - Fonti ufficiali: citate con link cliccabili")
    print("   - Fonti di riferimento interno: usate per arricchire le risposte ma non citate")
    print("\n📚 Le fonti di riferimento interno migliorano la qualità delle risposte")
    print("   fornendo contesto aggiuntivo senza essere esplicitamente menzionate.")

if __name__ == "__main__":
    main()
