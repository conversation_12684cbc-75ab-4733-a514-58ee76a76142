#!/usr/bin/env python3
"""
Script per verificare i modelli Gemini disponibili e testare la connessione.
"""

import os
import sys
from pathlib import Path

def load_environment():
    """Carica le variabili d'ambiente."""
    env_file = Path('.env')
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value.strip('"\'')

def test_gemini_connection():
    """Testa la connessione a Gemini e lista i modelli disponibili."""
    print("🔍 VERIFICA MODELLI GEMINI DISPONIBILI")
    print("=" * 50)
    
    # Carica variabili d'ambiente
    load_environment()
    
    gemini_api_key = os.getenv('GEMINI_API_KEY')
    if not gemini_api_key:
        print("❌ GEMINI_API_KEY non trovata nelle variabili d'ambiente")
        print("💡 Assicurati che sia presente nel file .env")
        return False
    
    try:
        import google.generativeai as genai
        
        # Configura API
        genai.configure(api_key=gemini_api_key)
        print(f"✅ API Key configurata (lunghezza: {len(gemini_api_key)} caratteri)")
        
        # Lista modelli disponibili
        print("\n📋 MODELLI DISPONIBILI:")
        print("-" * 30)
        
        available_models = []
        try:
            for model in genai.list_models():
                model_name = model.name.replace('models/', '')
                available_models.append(model_name)
                
                # Mostra dettagli del modello
                print(f"🤖 {model_name}")
                if hasattr(model, 'description'):
                    print(f"   📝 {model.description}")
                if hasattr(model, 'supported_generation_methods'):
                    methods = ', '.join(model.supported_generation_methods)
                    print(f"   🔧 Metodi: {methods}")
                print()
                
        except Exception as e:
            print(f"❌ Errore nel recupero dei modelli: {e}")
            return False
        
        # Verifica modelli specifici
        print("🎯 VERIFICA MODELLI SPECIFICI:")
        print("-" * 35)
        
        models_to_test = [
            'gemini-1.5-flash',
            'gemini-2.0-flash',
            'gemini-1.5-pro',
            'gemini-2.0-flash-exp',
            'gemini-exp-1206'
        ]
        
        working_models = []
        
        for model_name in models_to_test:
            try:
                print(f"🧪 Testando {model_name}...")
                
                # Crea il modello
                model = genai.GenerativeModel(model_name)
                
                # Test semplice
                response = model.generate_content("Ciao, rispondi solo 'OK'")
                
                if response and response.text:
                    print(f"   ✅ {model_name}: FUNZIONA")
                    print(f"   📝 Risposta: {response.text.strip()}")
                    working_models.append(model_name)
                else:
                    print(f"   ⚠️ {model_name}: Risposta vuota")
                    
            except Exception as e:
                print(f"   ❌ {model_name}: ERRORE - {e}")
            
            print()
        
        # Riepilogo
        print("📊 RIEPILOGO:")
        print("-" * 15)
        print(f"🔢 Modelli totali disponibili: {len(available_models)}")
        print(f"✅ Modelli testati funzionanti: {len(working_models)}")
        
        if working_models:
            print(f"\n🎯 MODELLI RACCOMANDATI:")
            for model in working_models:
                print(f"   • {model}")
                
            # Raccomandazione specifica
            if 'gemini-1.5-flash' in working_models:
                print(f"\n💡 RACCOMANDAZIONE: Usa 'gemini-1.5-flash' (stabile e veloce)")
            elif 'gemini-2.0-flash-exp' in working_models:
                print(f"\n💡 RACCOMANDAZIONE: Usa 'gemini-2.0-flash-exp' (sperimentale)")
            else:
                print(f"\n💡 RACCOMANDAZIONE: Usa '{working_models[0]}' (primo disponibile)")
        else:
            print(f"\n❌ NESSUN MODELLO FUNZIONANTE TROVATO")
            
        return len(working_models) > 0
        
    except ImportError:
        print("❌ Libreria google-generativeai non installata")
        print("💡 Installa con: pip install google-generativeai")
        return False
    except Exception as e:
        print(f"❌ Errore generale: {e}")
        return False

def fix_model_in_code(recommended_model):
    """Corregge il modello nel codice."""
    print(f"\n🔧 CORREZIONE AUTOMATICA DEL MODELLO")
    print("=" * 40)
    
    file_path = Path("pdf_chatbot_prodotti.py")
    if not file_path.exists():
        print(f"❌ File {file_path} non trovato")
        return False
    
    try:
        # Leggi il file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Trova e sostituisci il modello
        old_patterns = [
            "'gemini-2.0-flash'",
            '"gemini-2.0-flash"',
            "'gemini-1.5-flash'",
            '"gemini-1.5-flash"'
        ]
        
        new_model = f"'{recommended_model}'"
        
        updated = False
        for pattern in old_patterns:
            if pattern in content:
                content = content.replace(pattern, new_model)
                updated = True
                print(f"✅ Sostituito {pattern} con {new_model}")
        
        if updated:
            # Salva il file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"💾 File {file_path} aggiornato con successo")
            return True
        else:
            print(f"⚠️ Nessuna sostituzione necessaria in {file_path}")
            return True
            
    except Exception as e:
        print(f"❌ Errore nella correzione: {e}")
        return False

def show_next_steps():
    """Mostra i prossimi passi."""
    print(f"\n📋 PROSSIMI PASSI:")
    print("=" * 20)
    print("1. 🔄 Riavvia l'applicazione:")
    print("   python app.py")
    print("   # oppure")
    print("   ./restart_optimized.sh")
    print()
    print("2. 🧪 Testa la connessione:")
    print("   python test_timeout_fix.py")
    print()
    print("3. 🌐 Apri il browser:")
    print("   http://localhost:5000")
    print()
    print("4. 💬 Fai una domanda di test")

def main():
    """Funzione principale."""
    print("🔍 DIAGNOSI CONNESSIONE GEMINI - Chat-Jina")
    print("=" * 55)
    
    # Test connessione e modelli
    success = test_gemini_connection()
    
    if success:
        print(f"\n🎉 CONNESSIONE GEMINI FUNZIONANTE!")
        
        # Chiedi se correggere automaticamente
        response = input(f"\n❓ Vuoi correggere automaticamente il modello nel codice? (y/n): ")
        
        if response.lower() in ['y', 'yes', 's', 'si']:
            # Usa gemini-1.5-flash come default sicuro
            recommended_model = 'gemini-1.5-flash'
            
            if fix_model_in_code(recommended_model):
                print(f"\n✅ CORREZIONE COMPLETATA!")
                show_next_steps()
            else:
                print(f"\n❌ ERRORE NELLA CORREZIONE")
        else:
            print(f"\n💡 Correggi manualmente il modello in pdf_chatbot_prodotti.py")
            print(f"   Cambia 'gemini-2.0-flash' in 'gemini-1.5-flash'")
    else:
        print(f"\n❌ PROBLEMI CON LA CONNESSIONE GEMINI")
        print(f"🔍 Verifica:")
        print(f"   • API Key corretta nel file .env")
        print(f"   • Connessione internet attiva")
        print(f"   • Quota API non esaurita")

if __name__ == "__main__":
    main()
