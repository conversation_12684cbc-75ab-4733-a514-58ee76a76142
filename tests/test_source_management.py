#!/usr/bin/env python3
"""
Test per il sistema di gestione delle fonti documentali.
Verifica che:
1. Le fonti ufficiali (directory standard) vengano citate con link
2. Le fonti di riferimento interno (directory nolink) vengano usate silenziosamente
"""

import unittest
import os
import sys
import re
from pathlib import Path

# Aggiungi il percorso del progetto al PYTHONPATH
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pdf_chatbot_prodotti import ProductChatbot
from dotenv import load_dotenv

# Carica le variabili d'ambiente
load_dotenv()

class TestSourceManagement(unittest.TestCase):
    """Test per la gestione delle fonti documentali."""
    
    @classmethod
    def setUpClass(cls):
        """Setup della classe di test."""
        cls.jina_api_key = os.getenv('JINA_API_KEY')
        cls.gemini_api_key = os.getenv('GEMINI_API_KEY')
        
        if not cls.jina_api_key or not cls.gemini_api_key:
            raise unittest.SkipTest("API keys non disponibili")
        
        cls.chatbot = ProductChatbot(cls.jina_api_key, cls.gemini_api_key)
        cls.product_code = "ESEMPIO_PRODOTTO"
        
        # Prepara i documenti per il test
        cls.chatbot.prepare_product_documents(cls.product_code)
    
    def test_is_nolink_source_detection(self):
        """Test per la rilevazione delle fonti nolink."""
        # Test file in directory standard (fonte ufficiale)
        official_file = "pdf/ESEMPIO_PRODOTTO/manuale_utente.txt"
        self.assertFalse(self.chatbot._is_nolink_source(official_file))
        
        # Test file in directory nolink (fonte di riferimento interno)
        nolink_file = "pdf/ESEMPIO_PRODOTTO/nolink/riferimento_interno.txt"
        self.assertTrue(self.chatbot._is_nolink_source(nolink_file))
    
    def test_create_file_link_official_source(self):
        """Test creazione link per fonti ufficiali."""
        official_file = "pdf/ESEMPIO_PRODOTTO/manuale_utente.txt"
        link = self.chatbot._create_file_link(official_file, 1)
        
        self.assertIsNotNone(link)
        self.assertIn("/pdf/ESEMPIO_PRODOTTO/", link)
        self.assertIn("manuale_utente.txt", link)
        self.assertIn("#page=1", link)
    
    def test_create_file_link_nolink_source(self):
        """Test che non vengano creati link per fonti di riferimento interno."""
        nolink_file = "pdf/ESEMPIO_PRODOTTO/nolink/riferimento_interno.txt"
        link = self.chatbot._create_file_link(nolink_file, 1)
        
        self.assertIsNone(link)
    
    def test_response_contains_official_links(self):
        """Test che le risposte contengano link alle fonti ufficiali."""
        # Query che dovrebbe utilizzare fonti ufficiali
        query = "Dimmi qualcosa sul manuale utente"
        
        try:
            answer, _, _ = self.chatbot.search_and_answer(query, self.product_code)
            
            # Verifica che la risposta contenga link ai documenti ufficiali
            link_pattern = r'\[📄[^\]]+\]\([^)]+\)'
            links = re.findall(link_pattern, answer)
            
            # Se ci sono documenti ufficiali, dovrebbero esserci link
            if links:
                self.assertGreater(len(links), 0, "Dovrebbero esserci link alle fonti ufficiali")
                
                # Verifica che i link non puntino a file nolink
                for link in links:
                    self.assertNotIn("nolink", link, "I link non dovrebbero puntare a file nolink")
            
        except Exception as e:
            self.skipTest(f"Errore durante la query: {e}")
    
    def test_response_does_not_cite_nolink_sources(self):
        """Test che le risposte non citino esplicitamente le fonti di riferimento interno."""
        # Query che potrebbe utilizzare fonti di riferimento interno
        query = "Ci sono informazioni aggiuntive sul prodotto?"
        
        try:
            answer, _, _ = self.chatbot.search_and_answer(query, self.product_code)
            
            # Verifica che la risposta non contenga riferimenti espliciti ai file nolink
            self.assertNotIn("riferimento_interno.txt", answer)
            self.assertNotIn("nolink", answer)
            self.assertNotIn("INFORMAZIONI DI CONTESTO AGGIUNTIVE", answer)
            
        except Exception as e:
            self.skipTest(f"Errore durante la query: {e}")
    
    def test_context_generation_separates_sources(self):
        """Test che la generazione del contesto separi correttamente le fonti."""
        # Simula risultati di ricerca con entrambi i tipi di fonti
        mock_search_results = {
            'documents': [[
                "Contenuto da fonte ufficiale",
                "Contenuto da fonte di riferimento interno"
            ]],
            'metadatas': [[
                {'source': 'pdf/ESEMPIO_PRODOTTO/manuale_utente.txt', 'page_num': 1},
                {'source': 'pdf/ESEMPIO_PRODOTTO/nolink/riferimento_interno.txt', 'page_num': 1}
            ]]
        }
        
        # Testa la generazione del contesto
        try:
            context = self.chatbot._generate_answer("test query", mock_search_results, [])
            
            # Il contesto dovrebbe essere generato senza errori
            self.assertIsInstance(context, str)
            self.assertGreater(len(context), 0)
            
        except Exception as e:
            self.fail(f"Errore nella generazione del contesto: {e}")

if __name__ == '__main__':
    unittest.main()
