# 🧪 Test Suite - Chat <PERSON>

## 📋 Panoramica

Questa directory contiene tutti i test, utility di debug e strumenti di verifica per il sistema Chat Jina.

## 🧪 Test Funzionali

### Test Core del Sistema
- **`test_chatbot_responses.py`** - Test delle risposte del chatbot
- **`test_embedding_system.py`** - Test del sistema di embedding
- **`test_source_management.py`** - Test del sistema di gestione delle fonti documentali
- **`test_guardrails_integration.py`** - Test dell'integrazione dei guardrails
- **`test_logging_system.py`** - Test del sistema di logging delle conversazioni

### Test Performance e Ottimizzazioni
- **`test_performance_optimizations.py`** - Test delle ottimizzazioni di performance
- **`test_links_preservation.py`** - Test della preservazione dei link nei documenti
- **`test_document_links_regression.py`** - Test di regressione per i link ai documenti

### Test Specifici
- **`test_inappropriate_content_guardrails.py`** - Test dei guardrails per contenuti inappropriati
- **`test_language_detection_fix.py`** - Test del fix per la rilevazione della lingua
- **`test_links_fix.py`** - Test del fix per i link
- **`test_prompt_changes.py`** - Test delle modifiche ai prompt

## 🔧 Utility e Debug

### Strumenti di Connessione
- **`check_gemini_models.py`** - Verifica dei modelli Gemini disponibili
- **`test_gemini_2_connection.py`** - Test di connessione a Gemini 2.0

### Strumenti di Monitoraggio
- **`monitor_performance.py`** - Monitoraggio delle performance in tempo reale

### Demo e Test Interattivi
- **`test_source_management_demo.py`** - Demo del sistema di gestione delle fonti
- **`test_progress_indicator.html`** - Demo dell'indicatore di progresso UI

### Setup e Configurazione
- **`setup_logging.py`** - Setup del sistema di logging

## 🚀 Come Eseguire i Test

### Test Singoli
```bash
# Test specifico
python -m pytest tests/test_chatbot_responses.py -v

# Test con output dettagliato
python -m pytest tests/test_source_management.py -v -s
```

### Suite Completa
```bash
# Tutti i test
python -m pytest tests/ -v

# Solo test funzionali (esclude utility)
python -m pytest tests/test_*.py -v
```

### Demo Interattive
```bash
# Demo gestione fonti
python tests/test_source_management_demo.py

# Demo indicatore progresso (aprire in browser)
open tests/test_progress_indicator.html
```

## 🔍 Utility di Debug

### Verifica Connessioni
```bash
# Verifica modelli Gemini
python tests/check_gemini_models.py

# Test connessione Gemini 2.0
python tests/test_gemini_2_connection.py
```

### Monitoraggio
```bash
# Monitoraggio performance
python tests/monitor_performance.py
```

### Setup Sistema
```bash
# Setup logging
python tests/setup_logging.py
```

## 📊 Struttura dei Test

### Test Unitari
- Testano singole funzionalità
- Usano mock quando necessario
- Verificano comportamenti specifici

### Test di Integrazione
- Testano l'interazione tra componenti
- Usano dati reali quando possibile
- Verificano flussi completi

### Test di Performance
- Misurano tempi di risposta
- Verificano ottimizzazioni
- Monitorano utilizzo risorse

### Test di Regressione
- Verificano che i fix non rompano funzionalità esistenti
- Testano scenari problematici noti
- Garantiscono stabilità del sistema

## 🎯 Best Practices

### Esecuzione Test
1. **Ambiente pulito**: Eseguire test in ambiente isolato
2. **Variabili ambiente**: Assicurarsi che `.env` sia configurato
3. **Dipendenze**: Installare tutte le dipendenze con `pip install -r requirements.txt`

### Aggiunta Nuovi Test
1. **Naming**: Usare prefisso `test_` per i file di test
2. **Documentazione**: Aggiungere docstring descrittive
3. **Isolamento**: Ogni test deve essere indipendente
4. **Cleanup**: Pulire risorse dopo ogni test

## 🚨 Troubleshooting

### Problemi Comuni
- **API Keys**: Verificare che `JINA_API_KEY` e `GEMINI_API_KEY` siano configurate
- **Database**: Assicurarsi che MySQL sia in esecuzione per i test di logging
- **ChromaDB**: Verificare che la directory `chromadb_data` sia accessibile

### Debug
- Usare flag `-v -s` con pytest per output dettagliato
- Controllare i log in `logs/` per errori dettagliati
- Usare le utility di debug per isolare problemi specifici
