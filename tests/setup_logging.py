#!/usr/bin/env python3
"""
Setup script for the conversation logging system.
This script helps initialize the database and test the logging system.
"""

import os
import sys
import logging
import mysql.connector
from mysql.connector import Error
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_mysql_connection():
    """Test MySQL connection with the configured credentials."""
    try:
        connection = mysql.connector.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', 3306)),
            user=os.getenv('DB_USER', 'prova'),
            password=os.getenv('DB_PASSWORD', 'prova')
        )
        
        if connection.is_connected():
            logger.info("✅ MySQL connection successful")
            connection.close()
            return True
        else:
            logger.error("❌ MySQL connection failed")
            return False
            
    except Error as e:
        logger.error(f"❌ MySQL connection error: {e}")
        return False


def create_database():
    """Create the database and table if they don't exist."""
    try:
        # Connect without specifying database
        connection = mysql.connector.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', 3306)),
            user=os.getenv('DB_USER', 'prova'),
            password=os.getenv('DB_PASSWORD', 'prova')
        )
        
        cursor = connection.cursor()
        
        # Create database
        db_name = os.getenv('DB_NAME', 'chat_jina')
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        logger.info(f"✅ Database '{db_name}' created or already exists")
        
        # Use the database
        cursor.execute(f"USE {db_name}")
        
        # Create table
        create_table_query = """
        CREATE TABLE IF NOT EXISTS conversations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            product VARCHAR(255),
            question TEXT NOT NULL,
            response TEXT NOT NULL,
            guardrail_log JSON,
            session_id VARCHAR(255),
            user_agent TEXT,
            response_time_ms INT,
            confidence_score FLOAT,
            INDEX idx_timestamp (timestamp),
            INDEX idx_ip (ip_address),
            INDEX idx_product (product),
            INDEX idx_session (session_id)
        )
        """
        
        cursor.execute(create_table_query)
        logger.info("✅ Table 'conversations' created or already exists")
        
        cursor.close()
        connection.close()
        return True
        
    except Error as e:
        logger.error(f"❌ Error creating database: {e}")
        return False


def test_logging_system():
    """Test the conversation logging system."""
    try:
        from database import db_manager
        from logging_service import conversation_logger
        
        # Test database connection
        if not db_manager.test_connection():
            logger.error("❌ Database connection test failed")
            return False
        
        logger.info("✅ Database connection test passed")
        
        # Test logging a sample conversation
        sample_conversation = {
            'ip_address': '127.0.0.1',
            'product': 'TEST_PRODUCT',
            'question': 'Test question for logging system',
            'response': 'Test response from logging system',
            'guardrail_log': [
                {"action": "ALLOW", "details": {"metrics": {"characters": 32, "words": 5}}},
                {"action": "ALLOW", "details": {"detected_language": "it", "confidence": 0.57}},
                {"action": "ALLOW"}
            ],
            'session_id': 'test_session_123',
            'user_agent': 'Test User Agent',
            'response_time_ms': 150,
            'confidence_score': 0.95
        }
        
        # Log synchronously for testing
        record_id = db_manager.log_conversation(sample_conversation)
        
        if record_id:
            logger.info(f"✅ Test conversation logged successfully with ID: {record_id}")
            
            # Test retrieval
            conversations = db_manager.get_conversations(limit=1)
            if conversations:
                logger.info("✅ Test conversation retrieval successful")
                
                # Test statistics
                stats = db_manager.get_conversation_stats()
                if stats:
                    logger.info("✅ Statistics retrieval successful")
                    logger.info(f"   Total conversations: {stats.get('total_conversations', 0)}")
                    return True
                else:
                    logger.error("❌ Statistics retrieval failed")
                    return False
            else:
                logger.error("❌ Test conversation retrieval failed")
                return False
        else:
            logger.error("❌ Test conversation logging failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing logging system: {e}")
        return False


def main():
    """Main setup function."""
    logger.info("🚀 Starting conversation logging system setup...")
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        logger.error("❌ .env file not found. Please create it with database configuration.")
        return False
    
    # Test MySQL connection
    logger.info("🔍 Testing MySQL connection...")
    if not test_mysql_connection():
        logger.error("❌ MySQL connection failed. Please check your database configuration.")
        return False
    
    # Create database and table
    logger.info("🔧 Creating database and table...")
    if not create_database():
        logger.error("❌ Database creation failed.")
        return False
    
    # Test logging system
    logger.info("🧪 Testing logging system...")
    if not test_logging_system():
        logger.error("❌ Logging system test failed.")
        return False
    
    logger.info("✅ Conversation logging system setup completed successfully!")
    logger.info("📊 You can now use the following API endpoints:")
    logger.info("   - GET /api/conversations/stats - Get conversation statistics")
    logger.info("   - GET /api/conversations/search - Search conversations")
    logger.info("   - GET /api/conversations/daily-stats - Get daily statistics")
    logger.info("   - GET /api/conversations/products - Get product statistics")
    logger.info("   - GET /api/conversations/health - Health check")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
