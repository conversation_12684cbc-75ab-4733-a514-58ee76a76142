#!/usr/bin/env python3
"""
Monitor Performance per Chat-Jina
Monitora le performance del sistema in tempo reale.
"""

import time
import re
import sys
from pathlib import Path
from datetime import datetime, timedelta
import subprocess

def parse_log_line(line):
    """Estrae informazioni da una riga di log."""
    # Pattern per estrarre timestamp e messaggio
    timestamp_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})'
    
    # Pattern per tempi specifici
    patterns = {
        'chromadb_time': r'Query ChromaDB completata in ([\d.]+)s',
        'response_time': r'Risposta generata e salvata in cache \(tempo: (\d+)s\)',
        'guardrail_input_time': r'Input processing completed.*total_time[\'\"]: ([\d.]+)',
        'guardrail_output_time': r'Output processing completed.*total_time[\'\"]: ([\d.]+)',
        'user_query': r'\[CHAT\] Utente \([^)]+\): (.+)',
        'bot_response_start': r'\[CHAT\] Bot \([^)]+\):',
        'slow_query': r'Query ChromaDB LENTA completata in ([\d.]+)s'
    }
    
    result = {}
    
    # Estrai timestamp
    timestamp_match = re.search(timestamp_pattern, line)
    if timestamp_match:
        result['timestamp'] = timestamp_match.group(1)
    
    # Estrai informazioni specifiche
    for key, pattern in patterns.items():
        match = re.search(pattern, line)
        if match:
            if key in ['chromadb_time', 'guardrail_input_time', 'guardrail_output_time', 'slow_query']:
                result[key] = float(match.group(1))
            elif key == 'response_time':
                result[key] = int(match.group(1))
            elif key == 'user_query':
                result[key] = match.group(1)
            elif key == 'bot_response_start':
                result[key] = True
    
    return result

def monitor_logs(log_file="app.log", tail_lines=50):
    """Monitora i log per le performance."""
    print(f"📊 Monitoraggio Performance - {datetime.now().strftime('%H:%M:%S')}")
    print("=" * 60)
    
    if not Path(log_file).exists():
        print(f"❌ File di log {log_file} non trovato")
        print("💡 Assicurati che l'applicazione sia in esecuzione e generi log")
        return
    
    try:
        # Leggi le ultime righe del log
        result = subprocess.run(
            ["tail", "-n", str(tail_lines), log_file],
            capture_output=True, text=True, check=True
        )
        
        lines = result.stdout.strip().split('\n')
        
        # Analizza le performance
        stats = {
            'total_queries': 0,
            'chromadb_times': [],
            'response_times': [],
            'guardrail_times': [],
            'slow_queries': 0,
            'recent_queries': []
        }
        
        current_query = None
        query_start_time = None
        
        for line in lines:
            parsed = parse_log_line(line)
            
            if 'user_query' in parsed:
                current_query = parsed['user_query']
                query_start_time = parsed.get('timestamp')
                stats['total_queries'] += 1
                stats['recent_queries'].append({
                    'query': current_query[:50] + "..." if len(current_query) > 50 else current_query,
                    'timestamp': query_start_time
                })
            
            if 'chromadb_time' in parsed:
                stats['chromadb_times'].append(parsed['chromadb_time'])
            
            if 'response_time' in parsed:
                stats['response_times'].append(parsed['response_time'])
            
            if 'guardrail_input_time' in parsed:
                stats['guardrail_times'].append(parsed['guardrail_input_time'])
            
            if 'guardrail_output_time' in parsed:
                stats['guardrail_times'].append(parsed['guardrail_output_time'])
            
            if 'slow_query' in parsed:
                stats['slow_queries'] += 1
        
        # Mostra statistiche
        print(f"📈 Statistiche Performance (ultime {tail_lines} righe):")
        print(f"  • Query totali: {stats['total_queries']}")
        
        if stats['chromadb_times']:
            avg_chromadb = sum(stats['chromadb_times']) / len(stats['chromadb_times'])
            max_chromadb = max(stats['chromadb_times'])
            print(f"  • ChromaDB - Media: {avg_chromadb:.2f}s, Max: {max_chromadb:.2f}s")
        
        if stats['response_times']:
            avg_response = sum(stats['response_times']) / len(stats['response_times'])
            max_response = max(stats['response_times'])
            print(f"  • Gemini - Media: {avg_response:.1f}s, Max: {max_response:.1f}s")
            
            # Valutazione performance
            if avg_response <= 10:
                print("  ✅ Performance Gemini: OTTIMA")
            elif avg_response <= 15:
                print("  🟡 Performance Gemini: BUONA")
            elif avg_response <= 25:
                print("  🟠 Performance Gemini: ACCETTABILE")
            else:
                print("  🔴 Performance Gemini: LENTA")
        
        if stats['guardrail_times']:
            avg_guardrail = sum(stats['guardrail_times']) / len(stats['guardrail_times'])
            print(f"  • Guardrails - Media: {avg_guardrail:.3f}s")
        
        if stats['slow_queries'] > 0:
            print(f"  ⚠️ Query ChromaDB lente: {stats['slow_queries']}")
        
        # Mostra query recenti
        if stats['recent_queries']:
            print(f"\n📝 Query Recenti:")
            for i, query_info in enumerate(stats['recent_queries'][-5:], 1):
                print(f"  {i}. {query_info['timestamp']} - {query_info['query']}")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Errore lettura log: {e}")
    except Exception as e:
        print(f"❌ Errore monitoraggio: {e}")

def show_current_config():
    """Mostra la configurazione attuale."""
    print(f"\n⚙️ Configurazione Attuale:")
    print("-" * 30)
    
    try:
        from performance_config import PerformanceConfig
        
        config_items = [
            ("Gemini Timeout", f"{PerformanceConfig.GEMINI_TIMEOUT}s"),
            ("Max Output Tokens", PerformanceConfig.GEMINI_MAX_OUTPUT_TOKENS),
            ("Temperature", PerformanceConfig.GEMINI_TEMPERATURE),
            ("ChromaDB Max Results", PerformanceConfig.CHROMADB_MAX_RESULTS),
            ("ChromaDB Timeout", f"{PerformanceConfig.CHROMADB_QUERY_TIMEOUT}s"),
            ("Prompt Optimization", "✅" if PerformanceConfig.PROMPT_OPTIMIZATION else "❌"),
            ("Cache Size", PerformanceConfig.RESPONSE_CACHE_SIZE),
        ]
        
        for name, value in config_items:
            print(f"  • {name}: {value}")
            
    except Exception as e:
        print(f"❌ Errore lettura configurazione: {e}")

def check_system_status():
    """Verifica lo stato del sistema."""
    print(f"\n🔍 Stato Sistema:")
    print("-" * 20)
    
    # Verifica processi
    try:
        result = subprocess.run(
            ["pgrep", "-f", "python.*app.py"],
            capture_output=True, text=True
        )
        
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"  ✅ Applicazione in esecuzione (PID: {', '.join(pids)})")
        else:
            print("  ❌ Applicazione non in esecuzione")
    except Exception as e:
        print(f"  ⚠️ Errore verifica processi: {e}")
    
    # Verifica file di log
    log_files = ["app.log", "chat.log", "performance.log"]
    for log_file in log_files:
        if Path(log_file).exists():
            size = Path(log_file).stat().st_size
            print(f"  📄 {log_file}: {size} bytes")

def main():
    """Funzione principale."""
    print("📊 MONITOR PERFORMANCE - Chat-Jina")
    print("=" * 50)
    
    # Verifica argomenti
    if len(sys.argv) > 1:
        if sys.argv[1] == "config":
            show_current_config()
            return
        elif sys.argv[1] == "status":
            check_system_status()
            return
        elif sys.argv[1] == "watch":
            # Modalità watch continua
            try:
                while True:
                    print("\033[2J\033[H")  # Clear screen
                    monitor_logs()
                    show_current_config()
                    check_system_status()
                    print(f"\n🔄 Aggiornamento ogni 10 secondi... (Ctrl+C per uscire)")
                    time.sleep(10)
            except KeyboardInterrupt:
                print("\n👋 Monitoraggio interrotto")
                return
    
    # Monitoraggio singolo
    monitor_logs()
    show_current_config()
    check_system_status()
    
    print(f"\n💡 Comandi disponibili:")
    print(f"  python monitor_performance.py config  - Mostra solo configurazione")
    print(f"  python monitor_performance.py status  - Mostra solo stato sistema")
    print(f"  python monitor_performance.py watch   - Monitoraggio continuo")

if __name__ == "__main__":
    main()
