#!/usr/bin/env python3
"""
Test specifico per la connessione con Gemini 2.0 Flash.
"""

import os
import sys
import time
from pathlib import Path

def load_environment():
    """Carica le variabili d'ambiente."""
    env_file = Path('.env')
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value.strip('"\'')

def test_gemini_2_flash():
    """Testa specificamente Gemini 2.0 Flash."""
    print("🧪 TEST GEMINI 2.0 FLASH - Chat-Jina")
    print("=" * 45)
    
    # Carica variabili d'ambiente
    load_environment()
    
    gemini_api_key = os.getenv('GEMINI_API_KEY')
    if not gemini_api_key:
        print("❌ GEMINI_API_KEY non trovata")
        return False
    
    try:
        import google.generativeai as genai
        from performance_config import PerformanceConfig
        
        # Configura API
        genai.configure(api_key=gemini_api_key)
        print(f"✅ API Key configurata")
        
        # Usa le stesse configurazioni del sistema
        generation_config = PerformanceConfig.get_gemini_generation_config()
        safety_settings = PerformanceConfig.get_gemini_safety_settings()
        
        print(f"⚙️ Configurazioni:")
        print(f"   • Timeout: {PerformanceConfig.GEMINI_TIMEOUT}s")
        print(f"   • Max Tokens: {PerformanceConfig.GEMINI_MAX_OUTPUT_TOKENS}")
        print(f"   • Temperature: {PerformanceConfig.GEMINI_TEMPERATURE}")
        
        # Crea il modello
        print(f"\n🤖 Inizializzando Gemini 2.0 Flash...")
        model = genai.GenerativeModel(
            'gemini-2.0-flash',
            generation_config=generation_config,
            safety_settings=safety_settings
        )
        print(f"✅ Modello inizializzato")
        
        # Test progressivi
        tests = [
            {
                'name': 'Test Base',
                'prompt': 'Ciao! Rispondi solo "OK" per confermare che funzioni.',
                'expected_keywords': ['OK']
            },
            {
                'name': 'Test Italiano',
                'prompt': 'Scrivi una breve frase in italiano per confermare che comprendi la lingua.',
                'expected_keywords': ['italiano', 'comprendo', 'lingua']
            },
            {
                'name': 'Test Prodotti',
                'prompt': 'Se ti chiedessi informazioni su un prodotto, come risponderesti? Rispondi brevemente.',
                'expected_keywords': ['prodotto', 'informazioni', 'rispondere']
            }
        ]
        
        print(f"\n🧪 ESECUZIONE TEST:")
        print("-" * 25)
        
        all_passed = True
        
        for i, test in enumerate(tests, 1):
            print(f"\n{i}. {test['name']}:")
            print(f"   📝 Prompt: {test['prompt']}")
            
            try:
                start_time = time.time()
                
                # Genera risposta
                response = model.generate_content(test['prompt'])
                
                end_time = time.time()
                duration = end_time - start_time
                
                if response and response.text:
                    response_text = response.text.strip()
                    print(f"   ✅ Risposta ({duration:.1f}s): {response_text}")
                    
                    # Verifica parole chiave (opzionale)
                    found_keywords = []
                    for keyword in test['expected_keywords']:
                        if keyword.lower() in response_text.lower():
                            found_keywords.append(keyword)
                    
                    if found_keywords:
                        print(f"   🎯 Parole chiave trovate: {', '.join(found_keywords)}")
                    
                    print(f"   ⏱️ Tempo risposta: {duration:.2f}s")
                    
                    if duration > PerformanceConfig.GEMINI_TIMEOUT:
                        print(f"   ⚠️ Tempo superiore al timeout configurato!")
                        all_passed = False
                    
                else:
                    print(f"   ❌ Risposta vuota o non valida")
                    all_passed = False
                    
            except Exception as e:
                print(f"   ❌ Errore: {e}")
                all_passed = False
        
        # Test di stress (opzionale)
        print(f"\n🔥 TEST DI STRESS:")
        print("-" * 20)
        
        stress_prompt = """
        Analizza questo scenario: Un cliente vuole informazioni su un prodotto specifico.
        Fornisci una risposta professionale e dettagliata su come gestiresti questa richiesta.
        Includi almeno 3 punti chiave nella tua risposta.
        """
        
        try:
            print(f"📝 Prompt complesso...")
            start_time = time.time()
            
            response = model.generate_content(stress_prompt.strip())
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response and response.text:
                response_text = response.text.strip()
                word_count = len(response_text.split())
                
                print(f"✅ Risposta generata:")
                print(f"   📊 Parole: {word_count}")
                print(f"   ⏱️ Tempo: {duration:.2f}s")
                print(f"   📝 Anteprima: {response_text[:100]}...")
                
                if duration <= PerformanceConfig.GEMINI_TIMEOUT:
                    print(f"   🎯 Entro il timeout configurato!")
                else:
                    print(f"   ⚠️ Supera il timeout di {PerformanceConfig.GEMINI_TIMEOUT}s")
                    all_passed = False
                    
            else:
                print(f"❌ Test di stress fallito")
                all_passed = False
                
        except Exception as e:
            print(f"❌ Errore test di stress: {e}")
            all_passed = False
        
        # Risultato finale
        print(f"\n📊 RISULTATO FINALE:")
        print("=" * 25)
        
        if all_passed:
            print(f"🎉 GEMINI 2.0 FLASH FUNZIONA PERFETTAMENTE!")
            print(f"✅ Tutti i test superati")
            print(f"🚀 Il sistema è pronto per l'uso")
            
            # Mostra vantaggi di Gemini 2.0
            print(f"\n💡 VANTAGGI GEMINI 2.0 FLASH:")
            print(f"   • Velocità migliorata rispetto a 1.5")
            print(f"   • Qualità delle risposte superiore")
            print(f"   • Supporto multimodale avanzato")
            print(f"   • Gestione contesto migliorata")
            
        else:
            print(f"⚠️ ALCUNI TEST FALLITI")
            print(f"💡 Considera di usare gemini-1.5-flash come fallback")
            
        return all_passed
        
    except ImportError:
        print("❌ Libreria google-generativeai non installata")
        return False
    except Exception as e:
        print(f"❌ Errore generale: {e}")
        return False

def show_next_steps(success):
    """Mostra i prossimi passi."""
    print(f"\n📋 PROSSIMI PASSI:")
    print("=" * 20)
    
    if success:
        print("1. 🔄 Riavvia l'applicazione:")
        print("   python app.py")
        print()
        print("2. 🌐 Testa nel browser:")
        print("   http://localhost:5000")
        print()
        print("3. 💬 Fai domande complesse per testare Gemini 2.0")
        print()
        print("4. 📊 Monitora performance:")
        print("   python monitor_performance.py watch")
    else:
        print("1. 🔄 Considera di tornare a gemini-1.5-flash:")
        print("   Modifica pdf_chatbot_prodotti.py")
        print()
        print("2. 🔍 Verifica quota API:")
        print("   https://console.cloud.google.com/")
        print()
        print("3. 📞 Contatta supporto Google se necessario")

def main():
    """Funzione principale."""
    success = test_gemini_2_flash()
    show_next_steps(success)

if __name__ == "__main__":
    main()
