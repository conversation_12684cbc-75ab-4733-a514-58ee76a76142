#!/usr/bin/env python3
"""
Test script to verify that language detection is now less strict
and allows technical queries with API terms
"""

import sys
sys.path.append('.')

from guardrails_py.guardrail_manager import GuardrailManager
from guardrails_py.input.language_detection_guardrail import LanguageDetectionGuardrail
from guardrails_py.input.inappropriate_content_guardrail import InappropriateContentGuardrail

def test_language_detection_fix():
    """Test that language detection now allows technical queries"""
    
    print("🔧 Testing language detection fix...")
    
    # Test individual language detection guardrail
    lang_guard = LanguageDetectionGuardrail()
    
    # Test cases that were previously blocked
    previously_blocked_cases = [
        "posso accedere via api?",
        "come posso utilizzare le API?",
        "hai documentazione per le API REST?",
        "dove trovo le credenziali API?",
        "come configurare l'autenticazione API?",
        "posso usare OAuth per l'API?",
        "quali endpoint API sono disponibili?",
        "come fare una chiamata POST all'API?",
        "l'API supporta JSON?",
        "posso accedere al database via API?"
    ]
    
    print("\n📝 Testing individual language detection guardrail...")
    all_passed = True
    
    for query in previously_blocked_cases:
        result = lang_guard.process(query)
        action = result.get('action', 'UNKNOWN')
        details = result.get('details', {})
        confidence = details.get('confidence', 0)
        
        if action == 'ALLOW':
            print(f"✅ '{query}' → ALLOWED (confidence: {confidence:.3f})")
        else:
            print(f"❌ '{query}' → {action} (confidence: {confidence:.3f})")
            all_passed = False
    
    # Test complete guardrail system
    print("\n🛡️ Testing complete guardrail system...")
    
    manager = GuardrailManager(
        input_guardrails=[
            LanguageDetectionGuardrail(),
            InappropriateContentGuardrail(),
        ],
        output_guardrails=[]
    )
    
    test_cases = [
        {
            "query": "posso accedere via api?",
            "expected": "ALLOW",
            "description": "Original problematic query"
        },
        {
            "query": "come utilizzare le API REST?",
            "expected": "ALLOW", 
            "description": "Technical API question"
        },
        {
            "query": "hai un film porno?",
            "expected": "BLOCK",
            "description": "Inappropriate content (should still be blocked)"
        },
        {
            "query": "Quali sono le caratteristiche del prodotto?",
            "expected": "ALLOW",
            "description": "Normal product question"
        }
    ]
    
    system_passed = True
    for test_case in test_cases:
        query = test_case["query"]
        expected = test_case["expected"]
        description = test_case["description"]
        
        result = manager.process_input(query)
        blocked = result.get('blocked', False)
        actual = "BLOCK" if blocked else "ALLOW"
        
        if actual == expected:
            print(f"✅ {description}: '{query}' → {actual}")
        else:
            print(f"❌ {description}: '{query}' → {actual} (expected {expected})")
            system_passed = False
    
    # Summary
    print(f"\n📊 Test Results:")
    print(f"Individual language detection: {'✅ PASSED' if all_passed else '❌ FAILED'}")
    print(f"Complete guardrail system: {'✅ PASSED' if system_passed else '❌ FAILED'}")
    
    if all_passed and system_passed:
        print("\n🎉 All tests passed! Language detection is now properly configured.")
        print("Technical queries with API terms are now allowed while inappropriate content is still blocked.")
    else:
        print("\n⚠️ Some tests failed. Please check the configuration.")

if __name__ == "__main__":
    test_language_detection_fix()
