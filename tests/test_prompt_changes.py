#!/usr/bin/env python3
"""
Script di test per verificare le modifiche al prompt del chatbot
"""

import sys
import os
sys.path.append('.')

from pdf_chatbot_prodotti import ProductChatbot

def test_clean_ai_references():
    """Test della funzione di pulizia delle frasi AI"""
    
    # Crea un'istanza fittizia per testare il metodo
    class TestChatbot:
        def _clean_ai_references(self, response: str) -> str:
            """Rimuove frasi che rivelano la natura AI del chatbot"""
            import re
            
            # Pattern di frasi da rimuovere o sostituire
            patterns_to_remove = [
                r"Gentile utente,?\s*",
                r"Egregio utente,?\s*",
                r"Caro utente,?\s*",
                r"in base alla documentazione\s*(?:disponibile|fornita|in mio possesso)?,?\s*",
                r"in base ai documenti\s*(?:disponibili|forniti|in mio possesso)?,?\s*",
                r"secondo i manuali\s*(?:disponibili|forniti|in mio possesso)?,?\s*",
                r"dalle informazioni\s*(?:disponibili|fornite|in mio possesso)?,?\s*",
                r"in base al contesto\s*(?:fornito|disponibile)?,?\s*",
                r"secondo il contesto\s*(?:fornito|disponibile)?,?\s*",
                r"dai documenti\s*(?:forniti|disponibili|in mio possesso)?,?\s*",
                r"dalla documentazione\s*(?:fornita|disponibile|in mio possesso)?,?\s*",
                r"basandomi sui documenti\s*(?:forniti|disponibili)?,?\s*",
                r"consultando i manuali\s*(?:forniti|disponibili)?,?\s*",
            ]
            
            cleaned_response = response
            
            # Rimuovi i pattern problematici
            for pattern in patterns_to_remove:
                cleaned_response = re.sub(pattern, "", cleaned_response, flags=re.IGNORECASE)
            
            # Pulisci spazi multipli e virgole orfane
            cleaned_response = re.sub(r'\s+', ' ', cleaned_response)
            cleaned_response = re.sub(r',\s*,', ',', cleaned_response)
            cleaned_response = re.sub(r'^\s*,\s*', '', cleaned_response)
            cleaned_response = cleaned_response.strip()
            
            # Se la risposta inizia con una virgola o punto, rimuovila
            if cleaned_response.startswith((',', '.')):
                cleaned_response = cleaned_response[1:].strip()
            
            # Capitalizza la prima lettera se necessario
            if cleaned_response and cleaned_response[0].islower():
                cleaned_response = cleaned_response[0].upper() + cleaned_response[1:]
            
            return cleaned_response
    
    test_bot = TestChatbot()
    
    # Test cases
    test_cases = [
        {
            "input": "Gentile utente, in base alla documentazione disponibile, il prodotto funziona così...",
            "expected": "Il prodotto funziona così..."
        },
        {
            "input": "In base ai documenti forniti, la procedura è la seguente...",
            "expected": "La procedura è la seguente..."
        },
        {
            "input": "Secondo i manuali in mio possesso, questo componente...",
            "expected": "Questo componente..."
        },
        {
            "input": "Dalle informazioni disponibili, posso confermare che...",
            "expected": "Posso confermare che..."
        },
        {
            "input": "Caro utente, consultando i manuali forniti, la risposta è...",
            "expected": "La risposta è..."
        }
    ]
    
    print("🧪 Test della funzione _clean_ai_references:")
    print("=" * 60)
    
    all_passed = True
    for i, test_case in enumerate(test_cases, 1):
        result = test_bot._clean_ai_references(test_case["input"])
        passed = result == test_case["expected"]
        all_passed = all_passed and passed
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"Test {i}: {status}")
        print(f"  Input:    {test_case['input']}")
        print(f"  Expected: {test_case['expected']}")
        print(f"  Result:   {result}")
        print()
    
    if all_passed:
        print("🎉 Tutti i test sono passati!")
    else:
        print("⚠️  Alcuni test sono falliti.")
    
    return all_passed

def main():
    """Funzione principale di test"""
    print("🚀 Test delle modifiche al prompt del chatbot")
    print("=" * 60)
    
    # Test della funzione di pulizia
    test_clean_ai_references()
    
    print("\n📝 Note:")
    print("- Il prompt è stato modificato per essere più specifico")
    print("- Aggiunti esempi di cosa evitare")
    print("- Implementata funzione di post-processing per rimuovere frasi problematiche")
    print("- Il chatbot ora dovrebbe comportarsi come un consulente tecnico esperto")

if __name__ == "__main__":
    main()
