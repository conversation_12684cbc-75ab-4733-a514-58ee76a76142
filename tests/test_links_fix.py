#!/usr/bin/env python3
"""
Test specifico per verificare che i link ai PDF funzionino correttamente.
"""

import re
from pathlib import Path

def test_link_creation():
    """Testa la creazione dei link."""
    print("🔗 Test Creazione Link")
    print("=" * 30)
    
    # Simula il metodo _create_file_link
    def create_file_link(file_path: str, page_num: int) -> str:
        import urllib.parse
        product_code = Path(file_path).parent.name
        file_name = Path(file_path).name
        encoded_file_name = urllib.parse.quote(file_name)
        return f"/pdf/{product_code}/{encoded_file_name}#page={page_num}"
    
    # Test
    test_file = "pdf/ProdottoA/1.pdf"
    test_page = 1
    
    link = create_file_link(test_file, test_page)
    source_ref = f"[📄 {Path(test_file).name} - Pag. {test_page}]({link})"
    
    print(f"✓ File: {test_file}")
    print(f"✓ Link: {link}")
    print(f"✓ Riferimento completo: {source_ref}")
    
    return source_ref

def test_link_detection():
    """Testa il rilevamento dei link nelle risposte."""
    print("\n🔍 Test Rilevamento Link")
    print("=" * 30)
    
    # Test con link completi
    response_with_links = "Secondo [📄 1.pdf - Pag. 1](/pdf/ProdottoA/1.pdf#page=1), la procedura è..."
    
    # Test con link incompleti
    response_incomplete = "Secondo [📄 1.pdf - Pag. 1], la procedura è..."
    
    # Test senza link
    response_no_links = "La procedura è descritta nei documenti."
    
    link_pattern = r'\[📄[^\]]+\]\([^)]+\)'
    incomplete_pattern = r'\[📄[^\]]+\](?!\()'
    
    print(f"✓ Risposta con link: {len(re.findall(link_pattern, response_with_links))} link trovati")
    print(f"✓ Risposta incompleta: {len(re.findall(incomplete_pattern, response_incomplete))} link incompleti")
    print(f"✓ Risposta senza link: {len(re.findall(link_pattern, response_no_links))} link trovati")
    
    return True

def test_link_restoration():
    """Testa il ripristino dei link mancanti."""
    print("\n🔧 Test Ripristino Link")
    print("=" * 30)
    
    # Simula il contesto con link completi
    context = """[📄 1.pdf - Pag. 1](/pdf/ProdottoA/1.pdf#page=1)
Contenuto del documento...

[📄 2.pdf - Pag. 3](/pdf/ProdottoA/2.pdf#page=3)
Altro contenuto..."""
    
    # Simula una risposta con link incompleti
    response = "Secondo [📄 1.pdf - Pag. 1], puoi trovare info anche in [📄 2.pdf - Pag. 3]."
    
    # Simula il metodo _ensure_links_present
    def ensure_links_present(response: str, context: str) -> str:
        import re
        
        # Estrai tutti i link dal contesto
        link_pattern = r'\[📄[^\]]+\]\([^)]+\)'
        context_links = re.findall(link_pattern, context)
        
        # Cerca riferimenti incompleti
        incomplete_pattern = r'\[📄[^\]]+\](?!\()'
        incomplete_links = re.findall(incomplete_pattern, response)
        
        print(f"  Link nel contesto: {len(context_links)}")
        print(f"  Link incompleti nella risposta: {len(incomplete_links)}")
        
        if incomplete_links:
            for incomplete in incomplete_links:
                for complete_link in context_links:
                    if incomplete in complete_link:
                        response = response.replace(incomplete, complete_link)
                        print(f"  ✓ Ripristinato: {incomplete} → {complete_link}")
                        break
        
        return response
    
    restored_response = ensure_links_present(response, context)
    
    print(f"✓ Risposta originale: {response}")
    print(f"✓ Risposta ripristinata: {restored_response}")
    
    # Verifica che i link siano stati ripristinati
    link_pattern = r'\[📄[^\]]+\]\([^)]+\)'
    final_links = re.findall(link_pattern, restored_response)
    
    print(f"✓ Link finali nella risposta: {len(final_links)}")
    
    return len(final_links) > 0

def test_markdown_simulation():
    """Simula la conversione markdown."""
    print("\n📝 Test Simulazione Markdown")
    print("=" * 30)
    
    test_text = "Vedi il documento [📄 1.pdf - Pag. 1](/pdf/ProdottoA/1.pdf#page=1) per dettagli."
    
    # Simula la conversione markdown (senza importare markdown2)
    def simple_markdown_to_html(text):
        import re
        # Converte link markdown in HTML
        link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
        html_text = re.sub(link_pattern, r'<a href="\2">\1</a>', text)
        return html_text
    
    html_result = simple_markdown_to_html(test_text)
    
    print(f"✓ Testo markdown: {test_text}")
    print(f"✓ HTML risultante: {html_result}")
    
    # Verifica che il link sia stato convertito
    has_link = '<a href="/pdf/ProdottoA/1.pdf#page=1">' in html_result
    print(f"✓ Link HTML presente: {has_link}")
    
    return has_link

def main():
    """Funzione principale di test."""
    print("🧪 Test Fix Link PDF - Chat-Jina")
    print("=" * 40)
    
    tests = [
        ("Creazione Link", test_link_creation),
        ("Rilevamento Link", test_link_detection),
        ("Ripristino Link", test_link_restoration),
        ("Simulazione Markdown", test_markdown_simulation),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result is not False
        except Exception as e:
            print(f"❌ Errore in {test_name}: {e}")
            results[test_name] = False
    
    # Riepilogo
    print("\n" + "=" * 40)
    print("📊 RIEPILOGO TEST")
    print("=" * 40)
    
    for test_name, result in results.items():
        status = "✅ OK" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    if passed == total:
        print(f"\n🎉 Tutti i test OK ({passed}/{total})")
        print("Il sistema di link dovrebbe funzionare correttamente.")
    else:
        print(f"\n⚠️ Alcuni test falliti ({passed}/{total} OK)")
    
    print("\n💡 SUGGERIMENTI:")
    print("1. Verifica che i prompt includano istruzioni chiare sui link")
    print("2. Controlla che il sistema di pulizia AI preservi i link")
    print("3. Assicurati che markdown2 sia installato per la conversione HTML")
    print("4. Testa con query reali per verificare il comportamento di Gemini")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
