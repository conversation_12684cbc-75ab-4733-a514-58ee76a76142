<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Indicatore Progresso - Chat Jina</title>
    <link rel="stylesheet" href="static/css/style.css">
    <style>
        body {
            padding: 20px;
            background-color: #f0f2f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .test-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background-color 0.2s;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .progress-demo {
            margin-top: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🧪 Test Indicatore Progresso Semplificato</h1>
        
        <div style="text-align: center;">
            <button class="test-button" onclick="startProgressDemo()">
                🚀 Avvia Demo Progresso (3 Step)
            </button>
            <button class="test-button" onclick="clearDemo()">
                🗑️ Pulisci Demo
            </button>
        </div>
        
        <div id="progress-demo" class="progress-demo"></div>
        
        <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 6px;">
            <h3>📋 Schema Semplificato (3 Step):</h3>
            <ul>
                <li><strong>🔍 Ricerca</strong> - Ricerca nei documenti (3s)</li>
                <li><strong>🧠 Elaborazione</strong> - Elaborazione e analisi (20s)</li>
                <li><strong>✍️ Risposta</strong> - Generazione risposta (7s)</li>
            </ul>
            <p><strong>Totale:</strong> 30 secondi con timer in tempo reale</p>
        </div>
    </div>

    <script>
        function startProgressDemo() {
            const demoContainer = document.getElementById('progress-demo');
            
            // Crea l'indicatore di progresso
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message bot progress-message';
            messageDiv.innerHTML = `
                <div class="progress-container">
                    <div class="progress-text">🔍 Elaborazione in corso...</div>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-steps">
                        <span class="step active">🔍 Ricerca</span>
                        <span class="step">🧠 Elaborazione</span>
                        <span class="step">✍️ Risposta</span>
                    </div>
                    <div class="progress-timer">
                        <span class="timer-text">Tempo trascorso: <span class="elapsed-time">0s</span></span>
                    </div>
                </div>
            `;
            
            demoContainer.innerHTML = '';
            demoContainer.appendChild(messageDiv);

            // Timing semplificati per 3 passaggi essenziali
            const stepTimings = [
                { duration: 3000, text: "🔍 Ricerca nei documenti..." },
                { duration: 20000, text: "🧠 Elaborazione e analisi..." },
                { duration: 7000, text: "✍️ Generazione risposta..." }
            ];

            let currentStep = 0;
            let totalElapsed = 0;
            const steps = messageDiv.querySelectorAll('.step');
            const progressFill = messageDiv.querySelector('.progress-fill');
            const progressText = messageDiv.querySelector('.progress-text');
            const elapsedTimeSpan = messageDiv.querySelector('.elapsed-time');

            // Timer per il tempo trascorso
            const timerInterval = setInterval(() => {
                totalElapsed++;
                elapsedTimeSpan.textContent = `${totalElapsed}s`;
            }, 1000);

            // Funzione per avanzare al prossimo step
            function advanceStep() {
                if (currentStep < steps.length) {
                    steps[currentStep].classList.add('active');
                    progressText.textContent = stepTimings[currentStep].text;

                    const progressPercent = ((currentStep + 1) / steps.length) * 100;
                    progressFill.style.width = `${progressPercent}%`;

                    currentStep++;

                    if (currentStep < stepTimings.length) {
                        setTimeout(advanceStep, stepTimings[currentStep - 1].duration);
                    } else {
                        // Completa il progresso
                        setTimeout(() => {
                            clearInterval(timerInterval);
                            progressText.textContent = "✅ Completato!";
                            progressFill.style.width = "100%";
                        }, stepTimings[stepTimings.length - 1].duration);
                    }
                }
            }

            // Inizia il progresso dopo 500ms
            setTimeout(advanceStep, 500);

            // Salva l'intervallo per poterlo cancellare
            messageDiv.progressInterval = timerInterval;
        }

        function clearDemo() {
            const demoContainer = document.getElementById('progress-demo');
            demoContainer.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">Demo pulita. Clicca "Avvia Demo" per testare l\'indicatore.</p>';
        }

        // Inizializza con messaggio vuoto
        clearDemo();
    </script>
</body>
</html>
