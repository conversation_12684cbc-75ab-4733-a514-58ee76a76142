{"timestamp": "2025-07-22T12:20:23.198508", "processing_stats": {"total_files": 0, "processed_files": 0, "skipped_files": 3, "failed_files": 0, "total_chunks": 0, "total_size_bytes": 0, "start_time": "2025-07-22T12:20:23.198334", "end_time": null, "errors": [], "success_rate": 0.0}, "performance_stats": {"elapsed_time": 0.011879920959472656, "memory_usage_mb": 131.9765625, "memory_increase_mb": 0.234375, "cpu_percent": 0.0, "api_calls": 0}, "configuration": {"chunk_size": 1000, "chunk_overlap": 200, "batch_size": 50, "supported_extensions": [".pdf", ".txt", ".md", ".csv", ".doc", ".docx"]}, "system_info": {"python_version": "3.13.5 (main, Jun 21 2025, 09:35:00) [GCC 15.1.1 20250425]", "platform": "linux", "cpu_count": 16, "memory_total_gb": 31.274127960205078}}