{"timestamp": "2025-07-22T12:21:19.948718", "processing_stats": {"total_files": 3, "processed_files": 3, "skipped_files": 0, "failed_files": 0, "total_chunks": 0, "total_size_bytes": 7080138, "start_time": "2025-07-22T12:20:48.814711", "end_time": "2025-07-22T12:21:19.937512", "errors": [], "processing_time_seconds": 31.122801, "success_rate": 100.0}, "performance_stats": {"elapsed_time": 32.886048793792725, "memory_usage_mb": 260.18359375, "memory_increase_mb": 128.5390625, "cpu_percent": 0.0, "api_calls": 0}, "configuration": {"chunk_size": 1000, "chunk_overlap": 200, "batch_size": 50, "supported_extensions": [".pdf", ".txt", ".md", ".csv", ".doc", ".docx"]}, "system_info": {"python_version": "3.13.5 (main, Jun 21 2025, 09:35:00) [GCC 15.1.1 20250425]", "platform": "linux", "cpu_count": 16, "memory_total_gb": 31.274127960205078}}