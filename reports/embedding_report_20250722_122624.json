{"timestamp": "2025-07-22T12:26:24.399774", "processing_stats": {"total_files": 7, "processed_files": 7, "skipped_files": 0, "failed_files": 0, "total_chunks": 0, "total_size_bytes": 15151820, "start_time": "2025-07-22T12:25:31.266685", "end_time": "2025-07-22T12:26:24.376568", "errors": [], "processing_time_seconds": 53.109883, "success_rate": 100.0}, "performance_stats": {"elapsed_time": 54.69405198097229, "memory_usage_mb": 260.9375, "memory_increase_mb": 129.11328125, "cpu_percent": 0.0, "api_calls": 0}, "configuration": {"chunk_size": 1000, "chunk_overlap": 200, "batch_size": 50, "supported_extensions": [".pdf", ".txt", ".md", ".csv", ".doc", ".docx"]}, "system_info": {"python_version": "3.13.5 (main, Jun 21 2025, 09:35:00) [GCC 15.1.1 20250425]", "platform": "linux", "cpu_count": 16, "memory_total_gb": 31.274127960205078}}