{"timestamp": "2025-07-22T12:33:54.360799", "processing_stats": {"total_files": 7, "processed_files": 7, "skipped_files": 0, "failed_files": 0, "total_chunks": 0, "total_size_bytes": 15151820, "start_time": "2025-07-22T12:29:02.702657", "end_time": "2025-07-22T12:33:54.338232", "errors": [], "processing_time_seconds": 291.635575, "success_rate": 100.0}, "performance_stats": {"elapsed_time": 312.7465069293976, "memory_usage_mb": 265.546875, "memory_increase_mb": 133.6484375, "cpu_percent": 0.0, "api_calls": 0}, "configuration": {"chunk_size": 1000, "chunk_overlap": 200, "batch_size": 50, "supported_extensions": [".pdf", ".txt", ".md", ".csv", ".doc", ".docx"]}, "system_info": {"python_version": "3.13.5 (main, Jun 21 2025, 09:35:00) [GCC 15.1.1 20250425]", "platform": "linux", "cpu_count": 16, "memory_total_gb": 31.274127960205078}}