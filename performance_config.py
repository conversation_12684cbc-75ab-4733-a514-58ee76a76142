#!/usr/bin/env python3
"""
Configurazioni per le ottimizzazioni delle performance del sistema chat-jina.
Questo file permette di regolare facilmente i parametri di performance senza modificare il codice principale.
"""

class PerformanceConfig:
    """Configurazioni per le ottimizzazioni delle performance."""
    
    # === CONFIGURAZIONI GEMINI API ===
    GEMINI_TIMEOUT = 30  # Timeout in secondi per le chiamate a Gemini (aumentato per stabilità)
    GEMINI_MAX_OUTPUT_TOKENS = 1200  # Limite massimo di token per la risposta (bilanciato)
    GEMINI_TEMPERATURE = 0.3  # Creatività della risposta (bilanciata)
    GEMINI_TOP_P = 0.8  # Nucleus sampling (bilanciato)
    GEMINI_TOP_K = 30  # Top-k sampling (bilanciato)
    
    # === CONFIGURAZIONI CACHE ===
    RESPONSE_CACHE_SIZE = 100  # Numero massimo di risposte in cache
    SEARCH_CACHE_SIZE = 200  # Numero massimo di risultati di ricerca in cache
    QUERY_CACHE_SIZE = 100  # Numero massimo di query simili in cache
    SEARCH_CACHE_TTL = 300  # TTL in secondi per la cache di ricerca (5 minuti)
    SIMILARITY_THRESHOLD = 0.85  # Soglia di similarità per query simili (0.0-1.0)
    
    # === CONFIGURAZIONI GUARDRAILS ===
    GUARDRAILS_PARALLEL = True  # Esegui guardrails in parallelo
    GUARDRAILS_MAX_WORKERS = 3  # Numero massimo di worker per guardrails paralleli
    GUARDRAILS_TIMEOUT = 5.0  # Timeout in secondi per ogni guardrail
    
    # === CONFIGURAZIONI CHROMADB ===
    CHROMADB_RESULTS_OPTIMIZATION = True  # Ottimizza il numero di risultati in base alla query
    CHROMADB_MIN_RESULTS = 3  # Numero minimo di risultati
    CHROMADB_MAX_RESULTS = 4  # Numero massimo di risultati (ottimizzato per velocità)
    CHROMADB_SHORT_QUERY_RESULTS = 3  # Risultati per query brevi (≤3 parole)
    CHROMADB_MEDIUM_QUERY_RESULTS = 4  # Risultati per query medie (4-6 parole)
    CHROMADB_QUERY_TIMEOUT = 15  # Timeout in secondi per query ChromaDB (aumentato per stabilità)
    CHROMADB_SLOW_QUERY_THRESHOLD = 3  # Soglia per considerare una query lenta
    
    # === CONFIGURAZIONI PROMPT ===
    PROMPT_OPTIMIZATION = True  # Usa prompt ottimizzato e più conciso
    MAX_HISTORY_MESSAGES = 4  # Numero massimo di messaggi di cronologia da includere
    
    # === CONFIGURAZIONI LOGGING ===
    PERFORMANCE_LOGGING = True  # Abilita logging delle performance
    LOG_CACHE_HITS = True  # Logga i cache hits
    LOG_TIMING = True  # Logga i tempi di esecuzione
    
    # === CONFIGURAZIONI UI ===
    PROGRESS_INDICATOR = True  # Mostra indicatori di progresso
    PROGRESS_STEP_DURATION = 10000  # Durata media di ogni step in millisecondi (semplificato)
    PROGRESS_TOTAL_STEPS = 3  # Numero totale di step nell'indicatore di progresso (semplificato)
    PROGRESS_REALISTIC_TIMING = True  # Usa timing realistici basati sui log
    
    @classmethod
    def get_gemini_generation_config(cls):
        """Restituisce la configurazione per Gemini GenerationConfig."""
        import google.generativeai as genai
        return genai.types.GenerationConfig(
            temperature=cls.GEMINI_TEMPERATURE,
            top_p=cls.GEMINI_TOP_P,
            top_k=cls.GEMINI_TOP_K,
            max_output_tokens=cls.GEMINI_MAX_OUTPUT_TOKENS,
            candidate_count=1
        )
    
    @classmethod
    def get_gemini_safety_settings(cls):
        """Restituisce le impostazioni di sicurezza ottimizzate per Gemini."""
        return [
            {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
        ]
    
    @classmethod
    def get_guardrails_config(cls):
        """Restituisce la configurazione per i guardrails."""
        return {
            "parallel_execution": cls.GUARDRAILS_PARALLEL,
            "max_workers": cls.GUARDRAILS_MAX_WORKERS,
            "guardrail_timeout": cls.GUARDRAILS_TIMEOUT,
        }
    
    @classmethod
    def optimize_search_results(cls, query: str, default_n_results: int = 7) -> int:
        """Ottimizza il numero di risultati in base alla query."""
        if not cls.CHROMADB_RESULTS_OPTIMIZATION:
            return default_n_results
        
        query_words = len(query.split())
        
        if query_words <= 3:  # Query molto brevi
            return min(default_n_results, cls.CHROMADB_SHORT_QUERY_RESULTS)
        elif query_words <= 6:  # Query medie
            return min(default_n_results, cls.CHROMADB_MEDIUM_QUERY_RESULTS)
        else:  # Query lunghe
            return min(default_n_results, cls.CHROMADB_MAX_RESULTS)
    
    @classmethod
    def should_log_performance(cls) -> bool:
        """Verifica se il logging delle performance è abilitato."""
        return cls.PERFORMANCE_LOGGING
    
    @classmethod
    def should_log_cache_hits(cls) -> bool:
        """Verifica se il logging dei cache hits è abilitato."""
        return cls.LOG_CACHE_HITS
    
    @classmethod
    def should_log_timing(cls) -> bool:
        """Verifica se il logging dei tempi è abilitato."""
        return cls.LOG_TIMING


# === CONFIGURAZIONI AVANZATE ===
class AdvancedPerformanceConfig:
    """Configurazioni avanzate per utenti esperti."""
    
    # Configurazioni per il connection pooling del database
    DB_POOL_SIZE = 10
    DB_POOL_TIMEOUT = 30
    
    # Configurazioni per il threading
    MAX_CONCURRENT_REQUESTS = 5
    THREAD_POOL_SIZE = 10
    
    # Configurazioni per il rate limiting
    RATE_LIMIT_ENABLED = False
    RATE_LIMIT_REQUESTS_PER_MINUTE = 60
    
    # Configurazioni per il monitoring
    ENABLE_METRICS = False
    METRICS_COLLECTION_INTERVAL = 60  # secondi


# === PROFILI PREDEFINITI ===
class PerformanceProfiles:
    """Profili predefiniti per diversi scenari d'uso."""
    
    @staticmethod
    def apply_fast_profile():
        """Applica il profilo per massima velocità (qualità ridotta)."""
        PerformanceConfig.GEMINI_TIMEOUT = 10
        PerformanceConfig.GEMINI_MAX_OUTPUT_TOKENS = 800
        PerformanceConfig.GEMINI_TEMPERATURE = 0.2
        PerformanceConfig.CHROMADB_MAX_RESULTS = 4
        PerformanceConfig.MAX_HISTORY_MESSAGES = 2
        PerformanceConfig.SIMILARITY_THRESHOLD = 0.75
        PerformanceConfig.CHROMADB_QUERY_TIMEOUT = 6

    @staticmethod
    def apply_ultra_fast_profile():
        """Applica il profilo per velocità estrema (qualità molto ridotta)."""
        PerformanceConfig.GEMINI_TIMEOUT = 8
        PerformanceConfig.GEMINI_MAX_OUTPUT_TOKENS = 512
        PerformanceConfig.GEMINI_TEMPERATURE = 0.1
        PerformanceConfig.CHROMADB_MAX_RESULTS = 3
        PerformanceConfig.MAX_HISTORY_MESSAGES = 1
        PerformanceConfig.SIMILARITY_THRESHOLD = 0.70
        PerformanceConfig.CHROMADB_QUERY_TIMEOUT = 4
        PerformanceConfig.CHROMADB_SLOW_QUERY_THRESHOLD = 2
    
    @staticmethod
    def apply_balanced_profile():
        """Applica il profilo bilanciato (default)."""
        # Usa i valori di default della classe PerformanceConfig
        pass
    
    @staticmethod
    def apply_quality_profile():
        """Applica il profilo per massima qualità (velocità ridotta)."""
        PerformanceConfig.GEMINI_TIMEOUT = 60
        PerformanceConfig.GEMINI_MAX_OUTPUT_TOKENS = 4096
        PerformanceConfig.CHROMADB_MAX_RESULTS = 10
        PerformanceConfig.MAX_HISTORY_MESSAGES = 8
        PerformanceConfig.SIMILARITY_THRESHOLD = 0.90


if __name__ == "__main__":
    # Test delle configurazioni
    print("🔧 Configurazioni Performance Chat-Jina")
    print("=" * 50)
    print(f"Timeout Gemini: {PerformanceConfig.GEMINI_TIMEOUT}s")
    print(f"Cache Size: {PerformanceConfig.RESPONSE_CACHE_SIZE}")
    print(f"Guardrails Paralleli: {PerformanceConfig.GUARDRAILS_PARALLEL}")
    print(f"Ottimizzazione ChromaDB: {PerformanceConfig.CHROMADB_RESULTS_OPTIMIZATION}")
    print(f"Soglia Similarità: {PerformanceConfig.SIMILARITY_THRESHOLD}")
    
    # Test ottimizzazione risultati
    test_queries = [
        "help",  # 1 parola
        "come funziona",  # 2 parole
        "come installare il prodotto",  # 4 parole
        "qual è la procedura completa per l'installazione del sistema",  # 9 parole
    ]
    
    print("\n📊 Test Ottimizzazione Risultati:")
    for query in test_queries:
        optimized = PerformanceConfig.optimize_search_results(query)
        print(f"  '{query}' → {optimized} risultati")
